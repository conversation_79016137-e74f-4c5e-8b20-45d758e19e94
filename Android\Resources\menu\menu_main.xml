﻿<?xml version="1.0" encoding="utf-8" ?>
<!--For all properties see: http://developer.android.com/guide/topics/resources/menu-resource.html-->
<menu xmlns:android="http://schemas.android.com/apk/res/android">
  
  <item android:id="@+id/action_search"
         android:showAsAction="always"
        android:text="Search"
         android:actionViewClass="android.widget.SearchView"/>
  
</menu>

<!-- Code to implement into Activity:

Android.Widget.SearchView searchView;
public override bool OnCreateOptionsMenu(IMenu menu)
{
  this.MenuInflater.Inflate(Resource.Menu.menu_main, menu);

  var searchItem = menu.FindItem(Resource.Id.action_search);

  searchView = searchItem.ActionProvider.JavaCast<Android.Widget.SearchView>();

  searchView.QueryTextSubmit += (sender, args) =>
  {
    Toast.MakeText(this, "You searched: " + args.Query, ToastLength.Short).Show();

  };


  return base.OnCreateOptionsMenu(menu);
}

-->