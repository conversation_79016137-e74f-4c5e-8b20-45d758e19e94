﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models.Emloyee
{
    public class OrderItems
    {
        public string Title { get; set; }
        public int Price { get; set; }
        public string PriceS { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public string CountPrice { get { return (Price * Count).ToString("## ### ₽"); } set { } }
        public int TovarKod { get; set; }
        public int Count { get; set; }
        public string Image_URL { get { return (App.Current as App).DefauldAPI_URL + "Content/Images/Catalog/" + TovarKod + ".jpg"; } }

        public int Warranty { get; set; }
    }
    public class OrderInfoItemsEmp // page Order_info_page.xaml.cs. Information of the specofied order with user full name, phone number etc.
    {
        public int StatusID { get; set; } // The StatusID field from DB must be converted into the statuse string value in client apps.
        public bool IsHidden { get; set; }
        public string OrderDate { get; set; }
        public int UserID { get; set; }
        public string StatusChanged { get; set; }
        public string UsrPhoneNumber { get; set; }
        public string UsrFullName { get; set; }
        public List<OrderItems> ItemsInOrder { get; set; }
    }
}
