﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using FFImageLoading;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;

namespace MyStoreCom.ViewModels
{
    public class CatalogItemsVM
    {
        private static readonly Configuration config = new Configuration();

        public int UserID;

        public async Task<List<CatalogItem>> GetCatalog(string category)
        {
            using (HttpClient client = new HttpClient())
            {
                Uri uri = new Uri("" + config.DefauldAPI_URL + "catalog/" + category);

                //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", EncodeCredentials("login:password"));
                //client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", "user:user"); //EncodeCredentials("login:password"));
                List<CatalogItem> catalogItemslist = new List<CatalogItem>();

                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    catalogItemslist = JsonConvert.DeserializeObject<List<CatalogItem>>(jsonResponse);
                }
                var qqq = new Methods();

                foreach (var item in catalogItemslist)
                {
                   // ImageService.Instance.LoadUrl(config.DefauldAPI_URL + "Content/Images/Catalog/" + item.Kod_tovara + ".jpg").Into(Resource.findview);
                    //item.BitmapImage = await qqq.GetImageBitmapFromUrl(config.DefauldAPI_URL + "Content/Images/Catalog/" + item.Kod_tovara + ".jpg");
                }
                return catalogItemslist;
            }
        }

        public async Task<HttpStatusCode> AddToCart(int tovarcod)
        {
            using (HttpClient client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(tovarcod);
                var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                HttpResponseMessage response = await client.PostAsync(new Uri("https://mystorecomapi.ecsogy.ru/Cart?user_id=" + UserID + ""), stringContent);
                return response.StatusCode; //(HttpStatusCode.Ok);
            }
        }
    }
}