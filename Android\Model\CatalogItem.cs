﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;

namespace MyStoreCom.Model
{
    public class CatalogItem
    {
        public int Kod_tovara { get; set; }
        // public string Title { get; set; }
        public string Kod_tovaraSTR { get { return Kod_tovara.ToString("Код товара: ######"); } }
        public string Naimenovenie { get; set; }
        public int Price { get; set; }
        public string PriceSTR { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int Nalichie { get; set; }
        public string InStockS { get { return Nalichie.ToString("### шт'.'"); } set { Nalichie = Convert.ToInt32(value); } }
        public int Warranty { get; set; }
        public string WarrantyS { get { return Warranty.ToString("### мес'.'"); } set { Warranty = Convert.ToInt32(value); } }
        public string Proizvoditel { get; set; }
        public Bitmap BitmapImage { get; set; }
    }
}