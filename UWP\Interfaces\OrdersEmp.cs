﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MyStoreCOM.Interfaces
{
    public class OrdersEmpHandler : ICommand
    {
        public event EventHandler CanExecuteChanged;

        private Action _action;

        public OrdersEmpHandler(Action action)
        {
            _action = action;
        }

        public bool CanExecute(object parameter)
        {
            return true;
        }

        public void Execute(object parameter)
        {
            _action();
        }
    }
}
