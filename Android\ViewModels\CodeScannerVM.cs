﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;

namespace MyStoreCom.ViewModels
{
    public class CodeScannerVM
    {
        private static readonly Configuration config = new Configuration();

        public async Task<CatalogItem> SearchItemByBarcode (string barcode)
        {
            var wait = Task.Delay(3500);
            using (HttpClient client = new HttpClient())
            {
                CatalogItem FoundItem = null;
                Uri uri = new Uri(config.DefauldAPI_URL + "Search?barcode=" + barcode);

                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    FoundItem = JsonConvert.DeserializeObject<CatalogItem>(jsonResponse);
                }
                await wait;
                return FoundItem;
            }
        }
    }
}