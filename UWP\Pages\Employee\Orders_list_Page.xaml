﻿<Page
    x:Class="MyStoreCOM.Pages.Employee.Orders_list_Page"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages.Employee"
    xmlns:data="using:MyStoreCOM.Models.Emloyee"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <Grid>
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind _ActiveOrdersVM.IsLoading, Mode=OneWay}" IsActive="True"/>
        <Button x:Name="RetryBTN" Content="Попробовать ещё раз" Visibility="Collapsed" Click="RetryBTN_Click" HorizontalAlignment="Center" VerticalAlignment="Center"/>
        <Grid x:Name="ContentGrid" Visibility="Visible">
            <StackPanel x:Name="TopLayout" Margin="20,20,20,0" VerticalAlignment="Top" RelativePanel.AlignTopWithPanel="True">
                <TextBlock x:Name="Header" Text="Активные заказы" Style="{StaticResource SubheaderTextBlockStyle}"/>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="4"/>
                    </Grid.RowDefinitions>
                    <CommandBar 
                            Grid.Row="0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            DefaultLabelPosition="Right"
                            Background="{ThemeResource SystemControlBackgroundAltHighBrush}">
                        <AppBarButton Label="Обновить" 
                                  Icon="Refresh" 
                                  Command="{x:Bind _ActiveOrdersVM.RefreshCommand}"/>
                        <AppBarButton x:Name="MoreBTN"
                                      Label="Подробнее" 
                                      Icon="More"
                                      Click="MoreBTN_Click"
                                      IsEnabled="{x:Bind _ActiveOrdersVM.IsMoreButtonEnabled, Mode=OneWay}"/>
                        <AppBarButton Label="Печать"
                                  Icon="Print"
                                  Command="{x:Bind _ActiveOrdersVM.PrintCommand}"/>
                        <AppBarButton x:Name="DeleteBtn" 
                                              Label="Удалить" 
                                              Icon="Delete"
                                              Click="DeleteBtn_Click"
                                              IsEnabled="{x:Bind _ActiveOrdersVM.IsMoreButtonEnabled, Mode=OneWay}"/>
                    </CommandBar>
                    <ProgressBar x:Name="progressbar_loading" Grid.Row="1" Visibility="{x:Bind _ActiveOrdersVM.IsUpdating, Mode=OneWay}" HorizontalAlignment="Stretch" IsEnabled="True" ShowPaused="False" IsIndeterminate="True"/>

                </Grid>
            </StackPanel>
            <controls:DataGrid
                x:Name="DataGridOrders"
                Margin="0,110,0,0"
                AlternatingRowForeground="Gray"
                AutoGenerateColumns="False"
                IsReadOnly="False"
                HorizontalScrollBarVisibility="Auto"
                VerticalScrollBarVisibility="Visible"
                CanUserSortColumns="True"
                GridLinesVisibility="All"
                RowDetailsVisibilityMode="Collapsed"
                SelectionChanged="DataGridOrders_SelectionChanged"
                SelectedItem="{x:Bind _ActiveOrdersVM.SelectedItem, Mode=TwoWay}"
                SelectedIndex="{x:Bind _ActiveOrdersVM.SelectedRowIndex, Mode=TwoWay}"
                RowEditEnding="DataGridOrders_RowEditEnding"
                ItemsSource="{x:Bind _ActiveOrdersVM._ActiveOrdes}">
                <controls:DataGrid.Columns>
                    <controls:DataGridTextColumn Header="Номер заказа" Binding="{Binding OrderNumber}" IsReadOnly="True"/>
                    <controls:DataGridTextColumn Header="Дата создания заказа" Binding="{Binding OrderDate}" IsReadOnly="True"/>
                    <controls:DataGridComboBoxColumn Header="Статус"
                                                             Binding="{Binding StatusID, Mode=TwoWay}"
                                                             ItemsSource="{x:Bind OrderStatuses, Mode=OneWay}"
                                                             DisplayMemberPath="Status"/>
                    <controls:DataGridTextColumn Header="Дата обновления статуса" Binding="{Binding StatusChanged}" IsReadOnly="True"/>
                    <controls:DataGridTextColumn Header="ID Пользователя" Binding="{Binding UserID}" IsReadOnly="True"/>
                    <controls:DataGridComboBoxColumn Header="Удален пользователем"
                                                             Binding="{Binding IsHidden, Mode=TwoWay}"
                                                             ItemsSource="{x:Bind _ActiveOrdersVM.visibilityStatuses, Mode=TwoWay}"
                                                             DisplayMemberPath="IsHiddenSTR"/>
                </controls:DataGrid.Columns>
            </controls:DataGrid>
        </Grid>
    </Grid>
</Page>
