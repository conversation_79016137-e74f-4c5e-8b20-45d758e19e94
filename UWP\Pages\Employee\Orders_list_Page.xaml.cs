﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.ViewModels.Employee;
using System.Windows.Input;
using Microsoft.Toolkit.Uwp.Helpers;
using MyStoreCOM.Models.Emloyee;
using Windows.UI.Popups;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages.Employee
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>   
    public sealed partial class Orders_list_Page : Page
    {
        public ActiveOrdersViewModel _ActiveOrdersVM = new ActiveOrdersViewModel();
        private List<OrderStatus> OrderStatuses;

        public Orders_list_Page()
        {
            this.InitializeComponent();
            OrderStatuses = StatusesManager.GetOrderStatuses();
            _ActiveOrdersVM.IsMoreButtonEnabled = false;
            _ActiveOrdersVM.PopulateStaticLists();
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { _ActiveOrdersVM.IsLoading = true; }); // change the visibility of PrograssBar showing "loading"
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { ContentGrid.Visibility = Visibility.Collapsed; }); // change the visibility of content
            if (await _ActiveOrdersVM.LoadActiveOrders())
            {
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { _ActiveOrdersVM.IsLoading = false; }); // change the visibility of PrograssBar showing "loading"
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { ContentGrid.Visibility = Visibility.Visible; });
            }
            else
            {
                RetryBTN.Visibility = Visibility.Visible;
            }

            var PrintHelper = new PrintHelper(this);
            PrintHelper.RegisterForPrinting();
            PrintHelper.PreparePrintContent(new Orders_list_Page());
        }

        private void DataGridOrders_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count >= 1)
            {
                _ActiveOrdersVM.IsMoreButtonEnabled = true;
            }
            else _ActiveOrdersVM.IsMoreButtonEnabled = false;

        }

        private async void DataGridOrders_RowEditEnding(object sender, Microsoft.Toolkit.Uwp.UI.Controls.DataGridRowEditEndingEventArgs e)
        {
            if (!(await _ActiveOrdersVM.UploadStatus()))
            {
                var message = new MessageDialog("Упсс... Возникла ошибка, повторите позже");
                await message.ShowAsync();
            }
        }

        private async void RetryBTN_Click(object sender, RoutedEventArgs e)
        {
            RetryBTN.Visibility = Visibility.Collapsed;
            if (await _ActiveOrdersVM.LoadActiveOrders())
            {
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { ContentGrid.Visibility = Visibility.Visible; });
            }
            else
            {
                RetryBTN.Visibility = Visibility.Visible;
            }
        }

        private void MoreBTN_Click(object sender, RoutedEventArgs e)
        {
            Frame.Navigate(typeof(Order_Info_Page), _ActiveOrdersVM.SelectedItem);
        }

        private void DeleteBtn_Click(object sender, RoutedEventArgs e)
        {
            _ActiveOrdersVM.DeleteCompletly();
        }
    }
}
