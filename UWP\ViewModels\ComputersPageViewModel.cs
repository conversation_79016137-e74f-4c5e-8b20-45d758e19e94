﻿using System;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using MyStoreCOM.Models;
using Windows.Web.Http;
using Windows.Web.Http.Headers;
using Newtonsoft.Json;
using Windows.Data.Json;
using Microsoft.Toolkit.Uwp.Helpers;
using Windows.UI.Popups;

namespace MyStoreCOM.ViewModels
{
    public class ComputersPageViewModel : BindableBase
    {
        public readonly string _DefaultAPI_URL = (App.Current as App).DefauldAPI_URL;

        public string HeaderOfPage;

        public string GetAll { get; } = "SELECT[kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel],[image_count]FROM [MyStoreCOM].[dbo].[StoreCatalog]";

        public int SelectedRowIndex { get; set; }

        private bool _isloading;
        public bool IsLoading
        {
            get => _isloading;
            set => Set(ref _isloading, value);
        }

        static readonly HttpClient client = new HttpClient();

        public ObservableCollection<Computer> Computers { get; set; } = new ObservableCollection<Computer>();

        public static string EncodeCredentials(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public async Task<bool> LoadCatalogItems(string category)
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = true; }); // change the visibility of PrograssBar (Ring)

            Uri uri = new Uri(_DefaultAPI_URL + "catalog/" + category);
            client.DefaultRequestHeaders.Authorization = new HttpCredentialsHeaderValue("Basic", EncodeCredentials("login:password"));

            //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", EncodeCredentials("login:password"));
            try
            {
                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    var _computers = JsonConvert.DeserializeObject<ObservableCollection<Computer>>(jsonResponse);
                    foreach (var item in _computers)
                    {
                        Computers.Add(item);
                    }
                }
                return true;
            }
            catch
            {
                var message = new MessageDialog("Проверьте подключение к интернету");
                await message.ShowAsync();
                return false; // something went wrong, check internet connection etc.
            }
            finally
            {
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = false; }); // change the visibility of PrograssBar (Ring)
            }

        }

        public async Task<HttpStatusCode> Add_To_CartAsync(int tovarcod)
        {
            var JsonFormat = new HttpStringContent(JsonConvert.SerializeObject(tovarcod), Windows.Storage.Streams.UnicodeEncoding.Utf8, "application/json");
            HttpResponseMessage response = await client.PostAsync(new Uri(""+ _DefaultAPI_URL + "/Cart?user_id=" + (App.Current as App).Current_UserID +""),  JsonFormat);
            return response.StatusCode; //(HttpStatusCode.Ok);
        }
    }
}