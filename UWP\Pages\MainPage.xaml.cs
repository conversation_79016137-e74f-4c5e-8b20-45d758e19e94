﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.Models;
using Windows.UI.Xaml.Media.Animation;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class MainPage : Page
    {
        private List<Elements> MainPageElements;
        public MainPage()
        {
            this.InitializeComponent();
            MainPageElements = ElementsManager.GetElements();
        }
        
        private void GridView_ItemClick(object sender, ItemClickEventArgs e)
        {
            var element = (Elements)e.ClickedItem;

            //var element = Grid_View.ContainerFromItem(e.ClickedItem) as GridViewItem;
            //var _storeditem = element.Content as custom;

            var animation = Grid_View.PrepareConnectedAnimation("ca1", e.ClickedItem, "title");

            switch (element.Title)
            {
                case "Пользователи":
                    Frame.Navigate(typeof(Users));
                    break;
                case "Информация о сотрудниках":
                    //Frame.Navigate(typeof(EmployeesPage));
                    break;
                case "Наши магазины":
                    Frame.Navigate(typeof(StoresOnMapPG));
                    break;
            }
        }

        internal void SetSelectedNavigationItem(int v)
        {
            throw new NotImplementedException();
        }
    }
}
