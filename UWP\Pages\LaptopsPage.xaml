﻿<Page
    x:Class="MyStoreCOM.Pages.LaptopsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <RelativePanel Grid.Row="0">
            <TextBlock Margin="20,0,0,0" Style="{StaticResource TitleTextBlockStyle}" Text="Ноубуки" RelativePanel.AlignVerticalCenterWith="CommandBar" RelativePanel.AlignLeftWithPanel="True"/>
            <CommandBar x:Name="CommandBar" 
                        DefaultLabelPosition="Right"
                        Background="White"
                        RelativePanel.AlignRightWithPanel="True" 
                        RelativePanel.AlignVerticalCenterWithPanel="True">
                <AppBarButton Icon="Refresh" Label="Обновить"/>
                <AppBarButton Icon="Add" Label="Добавить"/>
                <AppBarButton Icon="Delete" Label="Удалить"/>
            </CommandBar>
        </RelativePanel>
        <controls:DataGrid
            Grid.Row="2"
            x:Name="Data_grid"
            GridLinesVisibility="All"
            AutoGenerateColumns="False"
            VerticalScrollBarVisibility="Auto"
            HorizontalScrollBarVisibility="Auto"
            AlternatingRowForeground="Gray"
            ItemsSource="{x:Bind Laptops.Laptops}">
            <controls:DataGrid.Columns>
                <controls:DataGridTextColumn Header="№" Binding="{Binding Count}"/>
                <controls:DataGridTextColumn Header="Код товара" Binding="{Binding TovarKod}"/>
                <controls:DataGridTextColumn Header="Наименование" Binding="{Binding Naimenovanie}"/>
                <controls:DataGridTextColumn Header="Цена" Binding="{Binding PriceS}"/>
                <controls:DataGridTextColumn Header="Наличие" Binding="{Binding InStockS}"/>
                <controls:DataGridTextColumn Header="Гарантия" Binding="{Binding WarrantyS}"/>
                <controls:DataGridTextColumn Header="Производитель" Binding="{Binding Manufacturer}"/>
            </controls:DataGrid.Columns>
        </controls:DataGrid>
    </Grid>
</Page>
