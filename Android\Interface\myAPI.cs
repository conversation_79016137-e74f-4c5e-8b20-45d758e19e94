﻿using Android.Graphics;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace MyStoreCom.Interface
{
    public class MyAPI
    {
        public async Task<List<CatalogItem>> GetUsersAsync()
        {
            using (HttpClient client = new HttpClient())
            {
                Configuration config = new Configuration();

                Uri uri = new Uri("" + config.DefauldAPI_URL + "api/CatalogItems");
                var computer = new List<CatalogItem>();

                //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", EncodeCredentials("login:password"));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", "user:user"); //EncodeCredentials("login:password"));

                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    computer = JsonConvert.DeserializeObject<List<CatalogItem>>(jsonResponse);
                }
                foreach (var item in computer)
                {
                    item.BitmapImage = await GetImageBitmapFromUrl("https://s.technopoint.ru/thumb/st1/fit/320/250/3690d70275dbb757277896f7375eb50a/49278049c02685fcc107af242ce755801f859617bfecb06bcc6fd183158b830a.jpg");
                }
                return computer;
            }
        }
        public async Task<Bitmap> GetImageBitmapFromUrl(string url)
        {
            Bitmap imageBitmap = null;
            using (var client = new HttpClient())
            {
                byte[] imageBytes = await client.GetByteArrayAsync(url);
                if (imageBytes != null && imageBytes.Length > 0)
                {
                    imageBitmap = await BitmapFactory.DecodeByteArrayAsync(imageBytes, 0, imageBytes.Length);
                }
            }
            return imageBitmap;
        }
    }
}