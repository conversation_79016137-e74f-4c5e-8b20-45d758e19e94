﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Collections.ObjectModel;
using MyStoreCOM.Models;

namespace MyStoreCOM.ViewModels
{
    public class LaptopsPageViewModel
    {
        public ObservableCollection<Laptop> Laptops { get; private set; } = new ObservableCollection<Laptop>();

        public string GetAll { get; } = "SELECT TOP (1000) [kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel]FROM[MyStoreCOM].[dbo].[Hardware]";

        public void LoadLaptops(string query)
        {
            using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
            {
                conn.Open();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = query;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            int i = 1;
                            while (reader.Read())
                            {
                                var laptop = new Laptop
                                {
                                    Count = i,
                                    TovarKod = reader.GetInt32(0),
                                    Naimenovanie = reader.GetString(1),
                                    Price = reader.GetInt32(2),
                                    InStock = reader.GetInt32(3),
                                    Warranty = reader.GetInt32(4),
                                    Manufacturer = reader.GetString(5)
                                };
                                i++;
                                Laptops.Add(laptop);
                            }
                        }
                    }
                }
                conn.Close();
            }
        }
    }
}
