﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9DF27858-1CF3-432D-B325-7390712545B6}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TemplateGuid>{122416d6-6b49-4ee2-a1e8-b825f31c79fe}</TemplateGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MyStoreCom</RootNamespace>
    <AssemblyName>MyStoreCom</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <AndroidApplication>True</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.designer.cs</AndroidResgenFile>
    <AndroidResgenClass>Resource</AndroidResgenClass>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <AndroidUseLatestPlatformSdk>false</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v13.0</TargetFrameworkVersion>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <MonoAndroidResourcePrefix>Resources</MonoAndroidResourcePrefix>
    <MonoAndroidAssetsPrefix>Assets</MonoAndroidAssetsPrefix>
    <AndroidEnableSGenConcurrent>true</AndroidEnableSGenConcurrent>
    <AndroidHttpClientHandlerType>Xamarin.Android.Net.AndroidClientHandler</AndroidHttpClientHandlerType>
    <AndroidUseLatestPlatformSdk>True</AndroidUseLatestPlatformSdk>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>False</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidUseSharedRuntime>True</AndroidUseSharedRuntime>
    <AndroidLinkMode>None</AndroidLinkMode>
    <EmbedAssembliesIntoApk>False</EmbedAssembliesIntoApk>
    <AotAssemblies>false</AotAssemblies>
    <EnableLLVM>false</EnableLLVM>
    <BundleAssemblies>false</BundleAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>True</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidManagedSymbols>true</AndroidManagedSymbols>
    <AndroidUseSharedRuntime>False</AndroidUseSharedRuntime>
    <AndroidLinkMode>SdkOnly</AndroidLinkMode>
    <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Mono.Android" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Data\Configuration.cs" />
    <Compile Include="Data\Methods.cs" />
    <Compile Include="Fragments\CatalogItemInfo.cs" />
    <Compile Include="Fragments\CodeScanner.cs" />
    <Compile Include="Fragments\Cart.cs" />
    <Compile Include="Fragments\Catalog.cs" />
    <Compile Include="Fragments\Login.cs" />
    <Compile Include="Fragments\Main_page.cs" />
    <Compile Include="Fragments\Orders.cs" />
    <Compile Include="Fragments\Search.cs" />
    <Compile Include="Fragments\StoresOnMap.cs" />
    <Compile Include="Interface\myAPI.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="Model\CartItem.cs" />
    <Compile Include="Model\CatalogItem.cs" />
    <Compile Include="Model\LoginUser.cs" />
    <Compile Include="Model\OrderItem.cs" />
    <Compile Include="Model\User.cs" />
    <Compile Include="MyActionBarDrawerToggle.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resources\Resource.designer.cs" />
    <Compile Include="ViewModels\CartItemsVM.cs" />
    <Compile Include="ViewModels\CatalogItemInfoVM.cs" />
    <Compile Include="ViewModels\CatalogItemsVM.cs" />
    <Compile Include="ViewModels\CodeScannerVM.cs" />
    <Compile Include="ViewModels\OrdersVM.cs" />
    <Compile Include="ViewModels\SearchVM.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AboutResources.txt" />
    <None Include="Properties\AndroidManifest.xml" />
    <None Include="Assets\AboutAssets.txt" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_shopping_cart_24.xml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateGeneratedFiles</Generator>
    </AndroidResource>
    <AndroidResource Include="Resources\layout\activity_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
    <AndroidResource Include="Resources\layout\orders_parent.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
    <AndroidResource Include="Resources\values\colors.xml" />
    <AndroidResource Include="Resources\values\ic_launcher_background.xml" />
    <AndroidResource Include="Resources\values\strings.xml" />
    <AndroidResource Include="Resources\values\styles.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\ic_launcher.xml" />
    <AndroidResource Include="Resources\mipmap-anydpi-v26\ic_launcher_round.xml" />
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-hdpi\ic_launcher_round.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-mdpi\ic_launcher_round.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xhdpi\ic_launcher_round.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxhdpi\ic_launcher_round.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\ic_launcher.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\ic_launcher_foreground.png" />
    <AndroidResource Include="Resources\mipmap-xxxhdpi\ic_launcher_round.png" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Xamarin.AndroidX.AppCompat" Version="*******" />
    <PackageReference Include="Xamarin.AndroidX.Core" Version="********" />
    <PackageReference Include="Xamarin.AndroidX.Browser" Version="*******" />
    <PackageReference Include="Xamarin.Google.Android.Material" Version="*******" />
    <PackageReference Include="Xamarin.AndroidX.RecyclerView" Version="********" />
    <PackageReference Include="Xamarin.Essentials" Version="1.8.0" />
    <PackageReference Include="FFImageLoading.Xamarin.Droid" Version="2.4.11.982" />
    <PackageReference Include="Xamarin.GooglePlayServices.Vision" Version="*********" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\menu\activity_main_drawer.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\content_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\drawer\" />
    <Folder Include="Resources\fragments\" />
    <Folder Include="Resources\mipmap-xxxhdpi\NewFolder1\" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\itemscatalog_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\catalogListView_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\menu\menu_main.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\app_bar_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\cart_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\cart_pattern.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\order_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\orders_child.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_delete_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\map.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_desktop_windows_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_laptop_windows_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_smartphone_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_tablet_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_desktop_mac_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_shop_two_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_add_shopping_cart_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\nav_header_main.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\baseline_perm_identity_24.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\nav_header_user.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\nav_header_backgroud_2.jpg" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\nav_header_backgroud.jpg" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\main_page.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\untitled_2554.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\code_scanner.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\search.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\catalog_item.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\barcode_scan.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\login.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\account_plus.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\login.axml">
      <SubType>Designer</SubType>
    </AndroidResource>
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\dots_horizontal_circle_outline.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\desktop_tower_monitor.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\map_search.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\magnify.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\desktop_tower.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
    Other similar extension points exist, see Microsoft.Common.targets.
    <Target Name="BeforeBuild">
    </Target>
    <Target Name="AfterBuild">
    </Target>
  -->
</Project>