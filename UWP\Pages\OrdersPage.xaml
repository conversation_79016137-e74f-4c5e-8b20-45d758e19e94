﻿<Page
    x:Class="MyStoreCOM.Pages.OrdersPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:data="using:MyStoreCOM.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:animations="using:Microsoft.Toolkit.Uwp.UI.Animations"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind Orders.IsLoading, Mode=OneWay}" IsActive="True"/>
        <StackPanel Margin="20">
            <TextBlock x:Name="Header" Margin="0,0,0,15" Text="История заказов" Style="{StaticResource SubheaderTextBlockStyle}"/>
            <ListView x:Name="MyMainList" ItemsSource="{x:Bind Orders.Ordersmain}" IsItemClickEnabled="False" SelectionMode="None" Visibility="Collapsed">
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="data:OrderMain">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="auto"/>
                                <RowDefinition Height="auto"/>
                            </Grid.RowDefinitions>
                            <StackPanel Grid.Row="0" Margin="0,12,0,0" HorizontalAlignment="Stretch" Padding="4" Orientation="Horizontal" Background="{ThemeResource SystemAccentColorLight3}">
                                <TextBlock Text="Заказ №" Margin="12,0,3,0" Style="{StaticResource BaseTextBlockStyle}" VerticalAlignment="Center"/>
                                <TextBlock Text="{x:Bind OrderNumber}" Margin="0,0,0,0" Style="{StaticResource BaseTextBlockStyle}"  VerticalAlignment="Center"/>
                                <TextBlock Text="от" Margin="5,0,3,0" Style="{StaticResource BaseTextBlockStyle}"  VerticalAlignment="Center"/>
                                <TextBlock Text="{x:Bind OrderDate}" Margin="0,0,12,0" Style="{StaticResource BaseTextBlockStyle}"  VerticalAlignment="Center"/>
                                <TextBlock Text="Статус заказа:" Margin="12,0,3,0" Style="{StaticResource BaseTextBlockStyle}" VerticalAlignment="Center"/>
                                <TextBlock Text="{x:Bind StatusID}" Margin="0,0,12,0" Style="{StaticResource BaseTextBlockStyle}"  VerticalAlignment="Center"/>
                                <Button x:Name="RemoveOrder"
                                Background="Transparent"
                                VerticalAlignment="Center"
                                Tag="{x:Bind OrderNumber}"
                                Click="RemoveOrder_Click"
                                Margin="70,0,0,0">
                                    <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE74D;"/>
                                </Button>
                            </StackPanel>
                            <ListView x:Name="SubItemsList_layout" ItemsSource="{x:Bind SubItemsList}" Grid.Row="1" SelectionMode="None" IsItemClickEnabled="True" ItemClick="SubItemsList_layout_ItemClick">
                                <ListView.ItemTemplate>
                                    <DataTemplate x:DataType="data:OrderSub">
                                        <RelativePanel Grid.Row="1" Margin="0,5,0,0" VerticalAlignment="Stretch">
                                            <controls:ImageEx x:Name="tovar_image" Margin="5" Source="{x:Bind Image}" Width="80"/>
                                            <TextBlock x:Name="Title"
                                               RelativePanel.RightOf="tovar_image"
                                               RelativePanel.AlignTopWithPanel="True"
                                               Margin="5,0,0,10"
                                               Text="{x:Bind Title}"
                                               Style="{StaticResource BodyTextBlockStyle}"
                                               animations:Connected.Key="MainImage"/>
                                            <TextBlock x:Name="tovarcod_field"
                                               Text="Код товара:"
                                               Margin="5,0,0,0"
                                               RelativePanel.RightOf="tovar_image"
                                               RelativePanel.Below="Title"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="tovarcod_number"
                                               Text="{x:Bind TovarKod}"
                                               Margin="3,0,0,0"
                                               RelativePanel.RightOf="tovarcod_field"
                                               RelativePanel.Below="Title"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="itemprice_field"
                                               Text="Цена:"
                                               Margin="5,0,0,0"
                                               RelativePanel.RightOf="tovar_image"
                                               RelativePanel.Below="tovarcod_number"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="itemprice_num"
                                               Text="{x:Bind PriceS}"
                                               Margin="3,0,0,0"
                                               RelativePanel.Below="tovarcod_number"
                                               RelativePanel.RightOf="itemprice_field"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="quantity_field"
                                               Text="Количество:"
                                               Margin="5,0,0,5"
                                               RelativePanel.RightOf="tovar_image"
                                               RelativePanel.Below="itemprice_field"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="quantity_each_num"
                                               Text="{x:Bind Count}"
                                               Margin="3,0,0,5"
                                               RelativePanel.Below="itemprice_field"
                                               RelativePanel.RightOf="quantity_field"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock
                                               Text="шт."
                                               Margin="3,0,0,5"
                                               RelativePanel.Below="itemprice_field"
                                               RelativePanel.RightOf="quantity_each_num"
                                               Style="{StaticResource CaptionTextBlockStyle}"/>
                                            <TextBlock x:Name="price_field"
                                               Text="Итого:"
                                               Padding="2"
                                               RelativePanel.LeftOf="price_number"
                                               Style="{StaticResource CaptionTextBlockStyle}"
                                               RelativePanel.AlignBottomWith="price_number"/>
                                            <TextBlock x:Name="price_number"
                                               Text="{x:Bind TotalPriceCountSTR}"
                                               Margin="3,0,5,0"
                                               Style="{StaticResource SubtitleTextBlockStyle}"
                                               FontWeight="Bold"
                                               RelativePanel.AlignRightWithPanel="True"
                                               RelativePanel.AlignVerticalCenterWithPanel="True"/>
                                        </RelativePanel>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                                <ListView.ItemContainerStyle>
                                    <Style TargetType="ListViewItem">
                                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    </Style>
                                </ListView.ItemContainerStyle>
                            </ListView>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <ListView.ItemContainerStyle>
                    <Style TargetType="ListViewItem">
                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    </Style>
                </ListView.ItemContainerStyle>
            </ListView>
        </StackPanel>
        <RelativePanel
            x:Name="EmptyOrdersPageLayout"
            Visibility="Collapsed">
            <TextBlock
                Text="Заказов нет" 
                Style="{StaticResource SubheaderTextBlockStyle}"
                RelativePanel.AlignHorizontalCenterWithPanel="True" 
                RelativePanel.AlignVerticalCenterWithPanel="True"/>
        </RelativePanel>
    </Grid>
</Page>
