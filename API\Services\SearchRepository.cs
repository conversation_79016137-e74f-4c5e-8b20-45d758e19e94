﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using MyStoreCOM_API_database.Models;

namespace MyStoreCOM_API_database.Services
{
    public class SearchRepository
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;

        public async Task<CatalogItem> SearchByBarcode(string barcodetosearch)
        {
            CatalogItem FoundItem = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(ConnectionString))
                {
                    await conn.OpenAsync();
                    string query = "USE MyStoreCOM SELECT[kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel] FROM [StoreCatalog] WHERE [barcode] = '" + barcodetosearch + "'";
                    if (conn.State == System.Data.ConnectionState.Open)
                    {
                        SqlCommand cmd = conn.CreateCommand();
                        cmd.CommandText = query;
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            await reader.ReadAsync();
                            FoundItem = new CatalogItem
                            {
                                Kod_tovara = reader.GetInt32(0),
                                Naimenovenie = reader.GetString(1),
                                Price = reader.GetInt32(2),
                                Nalichie = reader.GetInt32(3),
                                Warranty = reader.GetInt32(4),
                                Proizvoditel = reader.GetString(5)
                            };
                        }
                        conn.Close();
                    }
                }
            }
            catch
            {

            }
            return FoundItem;
        }
    }
}