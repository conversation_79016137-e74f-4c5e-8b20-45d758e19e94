﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Webkit;
using Android.Widget;

namespace MyStoreCom.Fragments
{
    public class StoresOnMap : Android.Support.V4.App.Fragment
    {
        View fragmentview;
        WebView web_view;
        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Create your fragment here
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            // Use this to return your custom view for this Fragment
            fragmentview = inflater.Inflate(Resource.Layout.map, container, false);
            web_view = fragmentview.FindViewById<WebView>(Resource.Id.webview_map);
            web_view.LoadUrl("https://yandex.ru/maps/976/bratsk/?display-text=DNS&ll=101.631310%2C56.151953&mode=search&sctx=ZAAAAAgBEAAaKAoSCQzDR8SUaFlAEQPqe9RfE0xAEhIJAAAAAAD%2Bvz8RALRVKmDbsT8oBTABONWFjr%2FS9539J0D0sQdIAVXNzMw%2BWABqAnJ1cACdAc3MzD2gAQCoAQA%3D&sll=101.631310%2C56.151953&sspn=0.106691%2C0.105358&text=%7B%22text%22%3A%22DNS%22%2C%22what%22%3A%5B%7B%22attr_name%22%3A%22chain_id%22%2C%22attr_values%22%3A%5B%221996262835%22%5D%7D%5D%7D&z=12.228138320529187");
            return fragmentview;
            //return base.OnCreateView(inflater, container, savedInstanceState);
        }
    }
}