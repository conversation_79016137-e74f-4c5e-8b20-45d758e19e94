﻿<Page
    x:Class="MyStoreCOM.Pages.CartPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:data="using:MyStoreCOM.Models"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:animations="using:Microsoft.Toolkit.Uwp.UI.Animations"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="ButtonBackground" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="{ThemeResource SystemAccentColorLight1}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="{ThemeResource SystemAccentColorDark1}"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>
    </Page.Resources>
    <Grid>
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind Cart.IsLoading, Mode=OneWay}" IsActive="True"/>
        <StackPanel>
            <TextBlock x:Name="Header" Margin="20,10,0,15" Text="Корзина" Style="{StaticResource SubheaderTextBlockStyle}"/>
            <Grid
                x:Name="MainCartGrid"
                Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                <ListView x:Name="Grid_VIEW" Grid.Column="0" ItemsSource="{x:Bind Cart.Items_in_Cart}" SelectionMode="None" IsItemClickEnabled="False" ItemClick="Grid_VIEW_ItemClick">
                    <ListView.ItemTemplate>
                        <DataTemplate x:Name="CartItem" x:DataType="data:Item_In_Cart">
                            <RelativePanel MinWidth="385" BorderBrush="{StaticResource SystemControlBackgroundAccentBrush}" BorderThickness="1" Padding="15" Margin="10">
                                <controls:ImageEx x:Name="item_image" Source="{x:Bind Image_URL}" Height="120" Width="165" RelativePanel.AlignLeftWithPanel="True"/>
                                <TextBlock x:Name="Title"
                            Margin="10,10"
                            RelativePanel.RightOf="item_image"
                            Text="{x:Bind Title}"
                            Style="{StaticResource BaseTextBlockStyle}"
                            animations:Connected.Key="MainImage"/>
                                <TextBlock x:Name="kod_tovara_field"
                            Text="Код товара:"
                            Margin="10,0,0,0"
                            RelativePanel.Below="Title"
                            RelativePanel.RightOf="item_image"/>
                                <TextBlock x:Name="TovarKod_Number"
                            Text="{x:Bind TovarCod}"
                            Margin="3,0,0,0" 
                            RelativePanel.RightOf="kod_tovara_field"
                            RelativePanel.Below="Title"/>
                                <TextBlock x:Name="priceCount_field"
                            Text="Цена:"
                            Margin="10,20,0,0"
                            RelativePanel.RightOf="item_image"
                            RelativePanel.Below="Title"/>
                                <TextBlock x:Name="PriceCount"
                            Text="{x:Bind PriceSTR}"
                            Margin="3,20,0,0"
                            RelativePanel.RightOf="priceCount_field"
                            RelativePanel.Below="Title"/>
                                <TextBlock x:Name="EachTovarCount"
                                   Text="{x:Bind EachHasSTR}"
                                   RelativePanel.AlignHorizontalCenterWithPanel="True"
                                   RelativePanel.AlignVerticalCenterWithPanel="True"/>
                                <Button x:Name="Remove_from_cart"
                            Background="Transparent"
                            Tag="{x:Bind TovarCod}"
                            ToolTipService.ToolTip="Убрать из корзины" 
                            ToolTipService.Placement="Bottom"
                            Click="Remove_from_cart_Click"
                            Margin="0,0,10,0"
                            RelativePanel.AlignRightWith="Price"
                            RelativePanel.AlignTopWithPanel="True">
                                    <StackPanel>
                                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE74D;"/>
                                    </StackPanel>
                                </Button>
                                <TextBlock x:Name="Price"
                            Text="{x:Bind PriceCountEachSTR}"
                            Margin="3,10,10,0"
                            Style="{StaticResource TitleTextBlockStyle}"
                            RelativePanel.AlignRightWithPanel="True"
                            RelativePanel.AlignBottomWithPanel="True"
                            FontWeight="Bold"/>
                                <TextBlock x:Name="price_field"
                            Text="Итого:"
                            Margin="0,10,0,0"
                            RelativePanel.LeftOf="Price"
                            RelativePanel.AlignBottomWith="Price"
                            Style="{StaticResource SubtitleTextBlockStyle}"/>
                            </RelativePanel>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        </Style>
                    </ListView.ItemContainerStyle>
                </ListView>
                <RelativePanel x:Name="FinalPrice" Grid.Column="1" Margin="0,10,0,0">
                    <Border x:Name="border1" Background="{StaticResource SystemAccentColorLight1}" Width="250" Height="152" Margin="10,0,10,30" RelativePanel.AlignHorizontalCenterWithPanel="True"/>
                    <TextBlock x:Name="finalPrice_field" 
                            Text="Итого:" 
                            Style="{StaticResource TitleTextBlockStyle}"
                            RelativePanel.AlignTopWith="border1"
                            Margin="40,15,0,10"/>
                    <TextBlock x:Name="final_price_num"
                       Text="{x:Bind Cart.TotalPriceSTR, Mode=OneWay}"
                        FontWeight="Bold"
                        Style="{StaticResource SubheaderTextBlockStyle}" 
                        RelativePanel.Below="finalPrice_field"
                        Margin="40,0,0,0"/>
                    <TextBlock x:Name="quantity_of_items_field1"
                       Text="Всего в корзине"
                       Margin="40,20,0,0"
                       RelativePanel.Below="final_price_num"
                       Style="{StaticResource CaptionTextBlockStyle}"/>
                    <TextBlock x:Name="quantity_of_itemsNUM"
                       Text="{x:Bind Cart.TotalQuantity, Mode=OneWay}"
                       Margin="3,20,0,0"
                       RelativePanel.RightOf="quantity_of_items_field1"
                       RelativePanel.Below="final_price_num"
                       Style="{StaticResource CaptionTextBlockStyle}"/>
                    <TextBlock x:Name="quantity_of_items_field2"
                       Text="{x:Bind Cart.QuantityField, Mode=OneWay}"
                       Margin="3,20,0,0"
                       RelativePanel.RightOf="quantity_of_itemsNUM"
                       RelativePanel.Below="final_price_num"
                       Style="{StaticResource CaptionTextBlockStyle}"/>
                    <Button x:Name="CreateOrder"
                            Content="Купить"
                            Click="CreateOrder_Click"
                            FontWeight="Bold"
                            Width="250"
                            Background="{StaticResource SystemAccentColorLight1}"
                            RelativePanel.Below="border1"
                            RelativePanel.AlignHorizontalCenterWith="border1"/>
                </RelativePanel>
            </Grid>
        </StackPanel>
        <RelativePanel
            x:Name="EmptyCartLayout"
            Visibility="Collapsed">
            <TextBlock
                Text="В корзине ещё ничего нет" 
                Style="{StaticResource SubheaderTextBlockStyle}"
                RelativePanel.AlignHorizontalCenterWithPanel="True" 
                RelativePanel.AlignVerticalCenterWithPanel="True"/>
        </RelativePanel>
        <controls:Loading x:Name="LoadingControl">
            <controls:Loading.Background>
                <SolidColorBrush Color="Transparent" Opacity="1"/>
            </controls:Loading.Background>
            <StackPanel Orientation="Vertical">
                <ProgressRing IsActive="True" Height="90" Width="90"/>
                <TextBlock Text="Создание заказа" Foreground="Black" HorizontalTextAlignment="Center" FontSize="20"/>
            </StackPanel>
        </controls:Loading>
    </Grid>
</Page>
