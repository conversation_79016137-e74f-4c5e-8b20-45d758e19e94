<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <EditText
        android:id="@+id/searchbox"
        android:layout_width="match_parent"
        android:layout_height="50sp"
        android:maxLength="25"
        android:background="@android:color/white"
        android:textSize="20dp"
        android:text="Привет"
        android:fontFamily="sans-serif-smallcaps"
        android:padding="10dp"/>
    <Button
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Найти"
        android:id="@+id/search_findbtn"
        android:layout_alignParentRight="true"/>
    <ImageButton
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:id="@+id/search_openScanerBTN"
        android:background="@android:color/transparent"
        android:layout_toLeftOf="@id/search_findbtn"
        android:src="@drawable/barcode_scan"/>
    <LinearLayout
        android:layout_below="@id/searchbox"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_gravity="center_horizontal"
        android:background="@android:color/black"/>
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/search_notfound"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:text="Не удалось ничего найти"
        android:textColor="@android:color/black"
        android:textSize="20dp"/>
    <ProgressBar
        android:id="@+id/search_progressbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"/>
</RelativeLayout>
