﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;

namespace MyStoreCom.ViewModels
{
    public class OrdersVM
    {
        public List<Order> OrdersList = new List<Order>();
        static readonly HttpClient client = new HttpClient();
        readonly Configuration config = new Configuration();
        public int UserID;

        public async Task<List<Order>> LoadOrders()
        {
            Uri uri = new Uri(config.DefauldAPI_URL + "Orders/all?userid=" + UserID + "");
            HttpResponseMessage responseMessage = await client.GetAsync(uri);
            if (responseMessage.IsSuccessStatusCode)
            {
                var jsonresponse = await client.GetStringAsync(uri);
                OrdersList = JsonConvert.DeserializeObject<List<Order>>(jsonresponse);
            }
            var qqq = new Methods();
            return OrdersList;
        }

        public async Task<bool> DeleteOrder(int orderID) // == hide order from user, not delete it!
        {
            Uri uri = new Uri(config.DefauldAPI_URL+ "orders/delete/" + orderID);
            var response = await client.DeleteAsync(uri);
            if (response.IsSuccessStatusCode)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}