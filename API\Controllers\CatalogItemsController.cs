﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using MyStoreCOM_API_database.Models;

namespace MyStoreCOM_API_database.Controllers
{
    [RoutePrefix("catalog")]
    public class CatalogItemsController : ApiController
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;

        public static string DecodeCredentials(string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }

        [Route("{category}")]
        [HttpGet]
        // GET: CatalogItems
        public async Task<IEnumerable<CatalogItem>> GetCatalogItems(string category)
        {
            float CategoryID = 99999; // wrong category must be entered. "0" is a category
            switch (category)
            {
                case "computers":
                    CategoryID = 2;
                    break;
                case "monoblocks":
                    CategoryID = 1.1f;
                    break;
                case "laptops":
                    CategoryID = 3;
                    break;
                case "smartphones":
                    CategoryID = 4;
                    break;
                case "tablets":
                    CategoryID = 5;
                    break;
                case "other":
                    CategoryID = 0;
                    break;
            }
            //var qauthHeader = Request.Headers.Authorization.Parameter; //get the encoded credentials from the header
            List<CatalogItem> items = new List<CatalogItem>();
            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                await conn.OpenAsync();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = "SELECT[kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel] FROM[MyStoreCOM].[dbo].[StoreCatalog] where [category] = "+ CategoryID + "";
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (await reader.ReadAsync())
                            {
                                var item = new CatalogItem
                                {
                                    Kod_tovara = reader.GetInt32(0),
                                    Naimenovenie = reader.GetString(1),
                                    Price = reader.GetInt32(2),
                                    Nalichie = reader.GetInt32(3),
                                    Warranty = reader.GetInt32(4),
                                    Proizvoditel = reader.GetString(5)
                                };
                                items.Add(item);
                            }
                        }
                    }
                }
                conn.Close();
            }
            return items;
        }

        // PUT: api/CatalogItems/5
        public void Put(int id, [FromBody]string value)
        {
        }

        // DELETE: api/CatalogItems/5
        public void Delete(int user_id, [FromBody]int tovarkod)
        {
            
        }
    }
}
