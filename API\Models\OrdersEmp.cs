﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Web;

namespace MyStoreCOM_API_database.Models
{
    public class OrdersEmp
    {
        public int OrderNumber { get; set; }
        //public int Count { get; set; }
        public int StatusID { get; set; }
        public bool IsHidden { get; set; }
        public string OrderDate { get; set; }
        public int UserID { get; set; }
        public string StatusChanged { get; set; }
    }

    public class OrderInfoItemsEmp // page Order_info_page.xaml.cs. Information of the specofied order with user full name, phone number etc.
    {
        public int StatusID { get; set; } // The StatusID filed from DB must be converted into the statuse string value in client apps.
        public bool IsHidden { get; set; }
        public string OrderDate { get; set; }
        public int UserID { get; set; }
        public string StatusChanged { get; set; }
        public string UsrPhoneNumber { get; set; }
        public string UsrFullName { get; set; }
        public List<OrderItemsEmp> ItemsInOrder { get; set; }
    }

    public class OrderItemsEmp
    {
        public string Title { get; set; }
        public int Price { get; set; }
        public int TovarKod { get; set; }
        public int Count { get; set; }
        public int Warranty { get; set; }
    }
}