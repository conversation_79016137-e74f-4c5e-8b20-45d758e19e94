﻿using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using MyStoreCOM_API_database.Models;

namespace MyStoreCOM_API_database.Controllers
{
    public class LoginController : ApiController
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString; //Conn string saved in web.config file
        public static string DecodeCredentials(string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }

        [Route("~/user/login")]
        public HttpResponseMessage Get()
        {
            try
            {
                string authHeader = Request.Headers.Authorization.Parameter; //get the encoded credentials from the header

                string credentials = DecodeCredentials(authHeader);

                int separatorIndex = credentials.IndexOf(':');

                string username = credentials.Substring(0, separatorIndex);
                string password = credentials.Substring(separatorIndex + 1);

                string query = "SELECT COUNT (*) from [MyStoreCOM].[dbo].[users] where [username] = '" + username + "' and [password] = '" + password + "'";
                string query2 = "SELECT [user_id],[type] from [MyStoreCOM].[dbo].[users] where [username] = '" + username + "' and [password] = '" + password + "'";

                using (SqlConnection conn = new SqlConnection(ConnectionString))
                {
                    int count = 0;
                    conn.Open();
                    if (conn.State == System.Data.ConnectionState.Open)
                    {
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = query;
                            SqlDataReader reader = cmd.ExecuteReader();
                            reader.Read();
                            count = reader.GetInt32(0);
                            reader.Close();
                        }
                        if (count >= 1)
                        {
                            var user = new User();
                            using (SqlCommand cmd = conn.CreateCommand())
                            {
                                cmd.CommandText = query2;
                                SqlDataReader reader = cmd.ExecuteReader();
                                while (reader.Read())
                                {
                                    user = new User
                                    {
                                        UserID = reader.GetInt32(0),
                                        UserType = reader.GetString(1).Trim()
                                    };
                                }
                            }
                            return Request.CreateResponse(HttpStatusCode.OK, user); // need to get the user real name!
                        }
                        else
                        {
                            return Request.CreateResponse(HttpStatusCode.NotFound);
                        }
                    }
                    else
                    {
                        return Request.CreateResponse(HttpStatusCode.InternalServerError);
                    }
                }
            }
            catch
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }
        }

        [Route("~/user/register")]
        public async Task<HttpResponseMessage> Post() // Register new user
        {
            string authHeader = Request.Headers.Authorization.Parameter; //get the encoded credentials from the header

            string credentials = DecodeCredentials(authHeader);

            int separatorIndex = credentials.IndexOf(':');

            string username = credentials.Substring(0, separatorIndex);
            string password = credentials.Substring(separatorIndex + 1);

            try
            {
                string query = "SELECT COUNT (*) from [MyStoreCOM].[dbo].[users] where [username] = '" + username + "' and [password] = '" + password + "'";
                string GetNewUserID = "SELECT [user_id] from [MyStoreCOM].[dbo].[users] where [username] = '" + username + "'";

                using (SqlConnection conn = new SqlConnection(ConnectionString))
                {
                    int count = 0, newuserid = 0;
                    await conn.OpenAsync();
                    string CheckIfExist = "select count (*) from [MyStoreCOM].[dbo].[users] where [username] = '" + username + "'"; // Check if the user login already exists
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = query;
                        SqlDataReader reader = cmd.ExecuteReader();
                        reader.Read();
                        count = reader.GetInt32(0);
                        reader.Close();
                    }

                    if (count >= 1) { return Request.CreateResponse(HttpStatusCode.NotAcceptable); }
                    else
                    {
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "USE MyStoreCom INSERT into users (username, password) values ('" + username + "', '" + password + "')";
                            await cmd.ExecuteNonQueryAsync();
                        }
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = GetNewUserID;
                            SqlDataReader reader = cmd.ExecuteReader();
                            reader.Read();
                            newuserid = reader.GetInt32(0);
                            reader.Close();
                        }
                        return Request.CreateResponse(HttpStatusCode.Created, newuserid);
                    }
                }
            }
            catch
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError);
            }
        }

        [Route("~/user/{id}/info")]
        public HttpResponseMessage GET(int id)
        {
            try
            {
                string query = "SELECT [first_name],[second_name],[middle_name],[phone] from [MyStoreCOM].[dbo].[users] where [user_id] = " + id;
                using (SqlConnection conn = new SqlConnection(ConnectionString))
                {
                    conn.Open();
                    if (conn.State == System.Data.ConnectionState.Open)
                    {
                        User user;
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = query;
                            SqlDataReader reader = cmd.ExecuteReader();
                            reader.Read();
                            user = new User
                            {
                                First_Name = reader.GetString(0).Trim(),
                                Second_name = reader.GetString(1).Trim(),
                                Middle_name = reader.GetString(2).Trim(),
                                PhoneNumber = reader.GetString(3).Trim()
                            };
                        }
                        return Request.CreateResponse(HttpStatusCode.OK, user); // need to get the user real name!
                    }
                    else
                    {
                        return Request.CreateResponse(HttpStatusCode.InternalServerError);
                    }
                }
            }
            catch
            {
                return Request.CreateResponse(HttpStatusCode.NotFound);
            }

        }
    }
}
