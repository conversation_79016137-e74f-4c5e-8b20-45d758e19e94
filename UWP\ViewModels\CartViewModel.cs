﻿using System;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models;
using Windows.Web.Http;
using Windows.Web.Http.Headers;
using Newtonsoft.Json;
using System.ComponentModel;
using Windows.UI.Popups;

namespace MyStoreCOM.ViewModels
{

    public class CartViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        public int TotalPrice;

        public readonly string _DefaultAPI_URL = (App.Current as App).DefauldAPI_URL;

        private string _totalPriceSTR;
        public string TotalPriceSTR
        {
            get { return _totalPriceSTR; }
            set
            {
                if (_totalPriceSTR == value)
                    return;
                _totalPriceSTR = value;
                PropertyChanged(this, new PropertyChangedEventArgs("TotalPriceSTR"));
            }
        }

        private string _totalquantity;
        public string TotalQuantity
        {
            get { return _totalquantity; }
            set
            {
                if (_totalquantity == value)
                    return;
                _totalquantity = value;
                PropertyChanged(this, new PropertyChangedEventArgs("TotalQuantity"));
            }
        }

        private string _quantityField;
        public string QuantityField
        {
            get { return _quantityField; }
            set
            {
                if (_quantityField == value)
                    return;
                _quantityField = value;
                PropertyChanged(this, new PropertyChangedEventArgs("QuantityField"));
            }
        }

        private bool _isloading = true;
        public bool IsLoading
        {
            get { return _isloading; }
            set
            {
                if (_isloading == value)
                    return;
                _isloading = value;
                PropertyChanged(this, new PropertyChangedEventArgs("IsLoading"));
            }
        }

        public ObservableCollection<Item_In_Cart> Items_in_Cart { get; private set; } = new ObservableCollection<Item_In_Cart>();

        static readonly HttpClient client = new HttpClient();

        public async Task<ObservableCollection<Item_In_Cart>> LoadItems()
        {
            var items = new ObservableCollection<Item_In_Cart>();
            HttpResponseMessage responseMessage = await client.GetAsync(new Uri(_DefaultAPI_URL  +"cart?user_id="+ (App.Current as App).Current_UserID));
            if (responseMessage.IsSuccessStatusCode)
            {
                var JsonResponse = await client.GetStringAsync(new Uri(_DefaultAPI_URL + "cart?user_id=" + (App.Current as App).Current_UserID));
                items = JsonConvert.DeserializeObject<ObservableCollection<Item_In_Cart>>(JsonResponse);
            }
            var count = items.Count;
            if (count != 0)
            {
                TotalQuantity = count.ToString();
                if ((count % 10) == 1 & count != 11 || count == 1)
                {
                    QuantityField = "элемент";
                }
                else if ((count % 10) == 2 || (count % 10) == 3 || (count % 10) == 4)
                {
                    QuantityField = "элемента";
                }
                else
                {
                    QuantityField = "элементов";
                }
            }
            return items;
        }

        public async Task<bool> DeleteItem (int tovarcod)
        {
            Uri uri = new Uri(_DefaultAPI_URL + "cart?user_id=" + (App.Current as App).Current_UserID + "&tovarkod=" + tovarcod + "");
            try
            {
                var deleting = client.DeleteAsync(uri);
                int indx = 0, i = 0;
                foreach (var item in Items_in_Cart)
                {
                    if (item.TovarCod == tovarcod)
                    {
                        indx = i;
                        break;
                    }
                    i++;
                }
                var response = await deleting;
                if (response.IsSuccessStatusCode)
                {
                    var itemtotalprice = Items_in_Cart[indx].PriceCountEach;
                    TotalPrice -= itemtotalprice;
                    TotalPriceSTR = TotalPrice.ToString("## ### ₽");
                    Items_in_Cart.RemoveAt(indx);
                    var count = Items_in_Cart.Count;
                    TotalQuantity = count.ToString();
                    if ((count % 10) == 1 & count != 11 || count == 1)
                    {
                        QuantityField = "элемент";
                    }
                    else if ((count % 10) == 2 || (count % 10) == 3 || (count % 10) == 4)
                    {
                        QuantityField = "элемента";
                    }
                    else
                    {
                        QuantityField = "элементов";
                    }
                }
                return true;
            }
            catch
            {
                var ErrorOccured = new MessageDialog("Произошла ошибка. Повторите позже");
                await ErrorOccured.ShowAsync();
                return false;
            }
        }

        public async Task<bool> CreateOrder()
        {
            Uri uri = new Uri(_DefaultAPI_URL + "Orders?userid=" + (App.Current as App).Current_UserID);
            try
            {
                HttpResponseMessage responseMessage = await client.PostAsync(uri, null);
                return true;
            }
            catch
            {
                var ErrorOccured = new MessageDialog("Произошла ошибка. Повторите позже");
                await ErrorOccured.ShowAsync();
                return false;
            }
        }
    }
}
