﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models
{
    public class Laptop
    {
        public int Count { get; set; }
        public int TovarKod { get; set; }
        public string Naimenovanie { get; set; }
        public int Price { get; set; }
        public string PriceS { get { return Price.ToString("##### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int InStock { get; set; }
        public string InStockS { get { return InStock.ToString("### шт'.'"); } set { InStock = Convert.ToInt32(value); } }
        public int Warranty { get; set; }
        public string WarrantyS { get { return Warranty.ToString("### мес'.'"); } set { Warranty = Convert.ToInt32(value); } }
        public string Manufacturer { get; set; }
    }
}
