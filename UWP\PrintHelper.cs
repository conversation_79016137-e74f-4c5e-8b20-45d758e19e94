﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.Graphics.Printing;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Printing;

namespace MyStoreCOM
{
    public class PrintHelper
    {
        /// <summary>
        /// PrintDocument is used to prepare the pages for printing.
        /// Prepare the pages to print in the handlers for the Paginate, GetPreviewPage, and AddPages events.
        /// </summary>
        protected PrintDocument printDocument;

        /// <summary>
        /// Marker interface for document source
        /// </summary>
        protected IPrintDocumentSource printDocumentSource;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="scenarioPage">The scenario page constructing us</param>
        public PrintHelper(Page scenarioPage)
        {
            //this.scenarioPage = scenarioPage;
            //printPreviewPages = new List<UIElement>();
        }
        public virtual void RegisterForPrinting()
        {
            printDocument = new PrintDocument();
            printDocumentSource = printDocument.DocumentSource;
            //printDocument.Paginate += CreatePrintPreviewPages;
            //printDocument.GetPreviewPage += GetPrintPreviewPage;
            //printDocument.AddPages += AddPrintPages;

            PrintManager printMan = PrintManager.GetForCurrentView();
            //printMan.PrintTaskRequested += PrintTaskRequested;
        }



        /// <summary>
        /// Method that will generate print content for the scenario
        /// For scenarios 1-4: it will create the first page from which content will flow
        /// Scenario 5 uses a different approach
        /// </summary>
        /// <param name="page">The page to print</param>
        public virtual void PreparePrintContent(Page page)
        {
            Page firstPage = null;
            if (firstPage == null)
            {
                firstPage = page;
                //StackPanel header = (StackPanel)firstPage.FindName("Header");
                //header.Visibility = Windows.UI.Xaml.Visibility.Visible;
            }

            // Add the (newly created) page to the print canvas which is part of the visual tree and force it to go
            // through layout so that the linked containers correctly distribute the content inside them.
            //PrintCanvas.Children.Add(firstPage);
            //PrintCanvas.InvalidateMeasure();
           // PrintCanvas.UpdateLayout();
        }



    }
}
