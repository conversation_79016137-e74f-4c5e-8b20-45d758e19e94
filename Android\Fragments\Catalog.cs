﻿using Android.Content;
using Android.OS;
using Android.Preferences;
using Android.Support.V4.Widget;
using Android.Support.V7.Widget;
using Android.Views;
using Android.Widget;
using FFImageLoading;
using MyStoreCom.Data;
using MyStoreCom.Model;
using MyStoreCom.ViewModels;
using System.Collections.Generic;

namespace MyStoreCom.Fragments
{
    public class Catalog : Android.Support.V4.App.Fragment
    {
        private static readonly Configuration config = new Configuration();

        View myFragmentView = null;

        static readonly CatalogItemsVM _CatalogItemVM = new CatalogItemsVM();

        RecyclerView.LayoutManager mLayoutManager;

        SwipeRefreshLayout swiper;

        CatalogItemsAdapter mAdapter;

        private string CatalogCategory;

        public Catalog(string category)
        {
            CatalogCategory = category;
        }

        public async override void OnCreate(Bundle savedInstanceState)
        {
            var ListOfItems = new List<CatalogItem>();
            try
            {
                base.OnCreate(savedInstanceState);
                ListOfItems = await _CatalogItemVM.GetCatalog(CatalogCategory); // loading catalog from RESTful
            }
            catch
            {
                Toast.MakeText(this.Activity, "Произошла ошибка. Попробуйте позже", ToastLength.Long).Show();
            }
            RecyclerView mRecyclerView = myFragmentView.FindViewById<RecyclerView>(Resource.Id.recyclerView);
            mAdapter = new CatalogItemsAdapter(ListOfItems, this.Activity);
            mRecyclerView.SetAdapter(mAdapter);
            mLayoutManager = new LinearLayoutManager(this.Activity);
            mRecyclerView.SetLayoutManager(mLayoutManager);
            DividerItemDecoration itemDecoration = new DividerItemDecoration(this.Activity, 1); // Separator line in list of items
            mRecyclerView.AddItemDecoration(itemDecoration);
            mAdapter.ItemClick += OnItemClick;
            swiper = myFragmentView.FindViewById<SwipeRefreshLayout>(Resource.Id.catalog_swiperefresh);
            swiper.Refresh += Swiper_Refresh;

            var progressbar = myFragmentView.FindViewById<ProgressBar>(Resource.Id.catalog_progress);
            progressbar.Visibility = ViewStates.Gone;
        }

        private async void Swiper_Refresh(object sender, System.EventArgs e)
        {
            var loadingItems = _CatalogItemVM.GetCatalog(CatalogCategory); // start loading items
            //mAdapter.ListItems.Clear();
            //mAdapter.ListItems.AddRange(await loadingItems); // wait until all items are arrived
            await loadingItems;
            mAdapter.NotifyDataSetChanged();
            swiper.Refreshing = false;
        }

        void OnItemClick(object sender, int position)
        {
            int photo = position;
            Toast.MakeText(this.Activity, "This is photo number " + photo, ToastLength.Short).Show();
        }
        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            // Use this to return your custom view for this Fragment
            myFragmentView = inflater.Inflate(Resource.Layout.catalogListView_main, container, false);

            ISharedPreferences prefs = PreferenceManager.GetDefaultSharedPreferences(Activity);
            _CatalogItemVM.UserID = prefs.GetInt("user_id", 0);

            return myFragmentView;
        }

        public class CatalogViewHolder : RecyclerView.ViewHolder
        {
            public ImageView Image { get; private set; }
            public TextView Title { get; private set; }
            public int KodTovara { get; set; }
            public TextView Price { get; private set; }
            public ImageButton AddToCart { get; private set; }

            public CatalogViewHolder(View itemView, System.Action<int> listener)
                : base(itemView)
            {
                // Locate and cache view references:
                Image = itemView.FindViewById<ImageView>(Resource.Id.imageView1);
                Title = itemView.FindViewById<TextView>(Resource.Id.textView11);
                Price = itemView.FindViewById<TextView>(Resource.Id.textView22);
                AddToCart = itemView.FindViewById<ImageButton>(Resource.Id.add_to_cartBTN);

                // AddToCart.SetOnClickListener(listener);
                // Detect user clicks on the item view and report which item
                // was clicked (by layout position) to the listener:
                itemView.Click += (sender, e) => listener(LayoutPosition);
            }
        }
        public class CatalogItemsAdapter : RecyclerView.Adapter
        {

            // Event handler for item clicks:
            public event System.EventHandler<int> ItemClick;
            public List<CatalogItem> ListItems;
            private Context mContext;

            public CatalogItemsAdapter(List<CatalogItem> listofitems, Context context)
            {
                mContext = context;
                ListItems = listofitems;
            }

            public override RecyclerView.ViewHolder
                OnCreateViewHolder(ViewGroup parent, int viewType)
            {
                // Inflate the CardView for the photo:
                View itemView = LayoutInflater.From(parent.Context).
                            Inflate(Resource.Layout.itemscatalog_main, parent, false);
                // Create a ViewHolder to find and hold these view references, and 
                // register OnClick with the view holder:
                CatalogViewHolder vh = new CatalogViewHolder(itemView, OnClick);
                return vh;
            }

            public override void OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
            {
                CatalogViewHolder vh = holder as CatalogViewHolder;

                // var hh = vh.AddToCart.HasOnClickListeners;
                if (!vh.AddToCart.HasOnClickListeners)
                {
                    vh.AddToCart.Click += delegate
                    {
                        AddToCart(vh.KodTovara, vh.Title.Text);
                    };
                }
                ImageService.Instance.LoadUrl(config.DefauldAPI_URL + "Content/Images/Catalog/" + ListItems[position].Kod_tovara + ".jpg").Into(vh.Image);
                //vh.Image.SetImageBitmap(ListItems[position].BitmapImage);
                vh.Title.Text = ListItems[position].Naimenovenie;
                vh.KodTovara = ListItems[position].Kod_tovara;
                vh.Price.Text = ListItems[position].PriceSTR;

            }

            public async void AddToCart(int kodTovara, string title)
            {
                var IsAddedtoCart = await _CatalogItemVM.AddToCart(kodTovara);
                if (IsAddedtoCart == System.Net.HttpStatusCode.Created)
                {
                    Toast.MakeText(mContext, "Элемент " + title + " добавлен в корзину", ToastLength.Short).Show();
                }
                else
                {
                    Toast.MakeText(mContext, "Не удалось добавить в корзину!", ToastLength.Long).Show();
                }
            }

            public override int ItemCount
            {
                get { return ListItems.Count; }
            }

            void OnClick(int position)
            {
                if (ItemClick != null)
                    ItemClick(this, position);
            }

            // private OnClickListener
        }
    }
}