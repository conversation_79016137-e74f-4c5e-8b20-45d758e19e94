﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;

namespace MyStoreCom.Model
{
    public class Order
    {
        public int Count { get; set; }
        public int OrderNumber { get; set; }
        public List<OrderSub> SubItemsList { get; set; }
        public string OrderDate { get; set; }
    }
    public class OrderSub
    {
        public string Title { get; set; }
        public int Price { private get; set; }
        public string PriceS { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int TovarKod { get; set; }
        public int Count { get; set; }
        public int Warranty { get; set; }
    }
}