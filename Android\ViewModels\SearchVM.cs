﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using MyStoreCom.Data;

namespace MyStoreCom.ViewModels
{
    public class SearchVM
    {
        private static readonly Configuration config = new Configuration();

        public async Task GetSearchResults(string searchText)
        {
            await Task.Delay(5000);
        }
    }
}