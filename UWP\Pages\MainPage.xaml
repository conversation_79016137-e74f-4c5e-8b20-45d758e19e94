﻿<Page
    x:Class="MyStoreCOM.Pages.MainPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:data="using:MyStoreCOM.Models"
    xmlns:animations="using:Microsoft.Toolkit.Uwp.UI.Animations"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="auto"/>
        </Grid.RowDefinitions>
        <RelativePanel Grid.Row="0">
            <Image Source="/Assets/Untitled-2.jpg" Stretch="UniformToFill" Height="250" HorizontalAlignment="Center" VerticalAlignment="Center"/>
            <TextBlock x:Name="Header" Foreground="White" Margin="30" Text="Интернет магазин" Style="{StaticResource HeaderTextBlockStyle}"/>
        </RelativePanel>
        <GridView Margin="20" x:Name="Grid_View" Grid.Row="1" ItemsSource="{x:Bind MainPageElements}" IsItemClickEnabled="True" ItemClick="GridView_ItemClick">
            <GridView.ItemTemplate>
                <DataTemplate x:DataType="data:Elements">
                    <RelativePanel x:Name="RelativePanel" Width="360" Height="125" HorizontalAlignment="Center" VerticalAlignment="Center" Background="{ThemeResource SystemControlBackgroundChromeMediumBrush}">
                        <FontIcon x:Name="icon" Foreground="{StaticResource SystemControlForegroundAccentBrush}" FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="{x:Bind Icon}" FontSize="55" Margin="15,10,15,15"/>
                        <TextBlock x:Name="title" FontFamily="{ThemeResource ContentControlThemeFontFamily}" FontSize="{ThemeResource GridViewHeaderItemThemeFontSize}" Margin="0,10,0,13" Text="{x:Bind Title}" RelativePanel.RightOf="icon" RelativePanel.AlignRightWithPanel="True"/>
                        <TextBlock x:Name="connectedElement" FontFamily="{ThemeResource ContentControlThemeFontFamily}" FontSize="{ThemeResource ContentControlFontSize}" Foreground="{ThemeResource SystemControlForegroundBaseHighBrush}" Text="{x:Bind Description}" TextWrapping="Wrap" RelativePanel.Below="title" RelativePanel.RightOf="icon" HorizontalAlignment="Stretch" Width="270"/>
                    </RelativePanel>
                </DataTemplate>
            </GridView.ItemTemplate>
        </GridView>
    </Grid>
</Page>
