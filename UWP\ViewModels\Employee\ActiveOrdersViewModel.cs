﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using Windows.Web.Http;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models.Emloyee;
using Windows.Web.Http.Headers;
using Newtonsoft.Json;
using System.Windows.Input;
using MyStoreCOM.Interfaces;
using Microsoft.Toolkit.Uwp.Helpers;
using Windows.UI.Popups;
using Windows.UI.Xaml.Controls;

namespace MyStoreCOM.ViewModels.Employee
{
    public class ActiveOrdersViewModel : BindableBase
    {
        public ObservableCollection<ActiveOrder> _ActiveOrdes { get; private set; } = new ObservableCollection<ActiveOrder>();

        public readonly string _DefaultAPI_URL = (App.Current as App).DefauldAPI_URL;

        static readonly HttpClient client = new HttpClient();

        private bool _isUpdating;
        public bool IsUpdating
        {
            get => _isUpdating;
            set => Set(ref _isUpdating, value);
        }

        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set => Set(ref _isLoading, value);
        }

        private bool _isMoreButtonEnabled;
        public bool IsMoreButtonEnabled // Also delete button is binded to the value
        {
            get => _isMoreButtonEnabled;
            set => Set(ref _isMoreButtonEnabled, value);
        }

        public List<VisibilityStatus> visibilityStatuses { get; set; }

        public ActiveOrder SelectedItem { get; set; }

        public int SelectedRowIndex { get; set; }

        public async Task<bool> LoadActiveOrders()
        {
            try
            {
                Uri uri = new Uri("" + _DefaultAPI_URL + "employee/orders/all");
                client.DefaultRequestHeaders.Authorization = new HttpCredentialsHeaderValue("Basic", EncodeCredentials("login:password"));
                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    var __ActiveOrdes = JsonConvert.DeserializeObject<ObservableCollection<ActiveOrder>>(jsonResponse);
                    _ActiveOrdes.Clear();
                    foreach (var item2 in __ActiveOrdes)
                    {
                        _ActiveOrdes.Add(item2);
                    }
                }
                return true;
            }
            catch
            {
                var message = new MessageDialog("Проверьте подключение к интернету");
                await message.ShowAsync();
                return false; // something went wrong, check internet connection etc.
            }
            finally
            {
                //await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = false; }); // change the visibility of PrograssBar
            }
        }
        public async Task<bool> UploadStatus()
        {
            HttpResponseMessage responseMessage = null;
            try
            {
                if (!IsUpdating) await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = true; });
                var JsonFormat = new HttpStringContent(JsonConvert.SerializeObject(SelectedItem.StatusID), Windows.Storage.Streams.UnicodeEncoding.Utf8, "application/json");
                Uri uri = new Uri("https://mystorecomapi.ecsogy.ru/employee/order/" + SelectedItem.OrderNumber + "/status");
                responseMessage = await client.PutAsync(uri, JsonFormat);
                await LoadActiveOrders();
                await Task.Delay(2000);
                return true;
            }
            catch
            {
                return false;
            }
            finally
            {
                //  if (responseMessage != null)
                // {
                //    return true;
                // }
                //else return false;
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = false; });
            }
            //return responseMessage.IsSuccessStatusCode;
        }

        public ICommand RefreshCommand
        {
            get
            {
                return new OrdersEmpHandler(() => Refresh());
            }
        }

        public ICommand PrintCommand
        {
            get
            {
                return new OrdersEmpHandler(() => PrintOrders());
            }
        }

        private async void Refresh()
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = true; }); // change the visibility of PrograssBar
            await LoadActiveOrders();
            await Task.Delay(2000);
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = false; }); // change the visibility of PrograssBar
        }

        private async void PrintOrders()
        {
            if (Windows.Graphics.Printing.PrintManager.IsSupported())
            {
                try
                {
                    // Show print UI
                    await Windows.Graphics.Printing.PrintManager.ShowPrintUIAsync();

                }
                catch
                {
                    // Printing cannot proceed at this time
                    ContentDialog noPrintingDialog = new ContentDialog()
                    {
                        Title = "Printing error",
                        Content = "\nSorry, printing can' t proceed at this time.",
                        PrimaryButtonText = "OK"
                    };
                    await noPrintingDialog.ShowAsync();
                }
            }
            else
            {
                // Printing is not supported on this device
                ContentDialog noPrintingDialog = new ContentDialog()
                {
                    Title = "Printing not supported",
                    Content = "\nSorry, printing is not supported on this device.",
                    PrimaryButtonText = "OK"
                };
                await noPrintingDialog.ShowAsync();
            }
        }

        public async void DeleteCompletly() // hide the order from all of the employees, only DB admin can restore the order
        {
            //await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = true; }); // change the visibility of PrograssBar (Ring)

            Uri uri = new Uri("https://mystorecomapi.ecsogy.ru/employee/order/delete/" + SelectedItem.OrderNumber + "");
            _ActiveOrdes.RemoveAt(SelectedRowIndex);
            var response = await client.DeleteAsync(uri);
            response.EnsureSuccessStatusCode();
            if (response.IsSuccessStatusCode)
            {
                //await LoadOrders();
            }
            //await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = false; }); // change the visibility of PrograssBar (Ring)
        }

        public static string EncodeCredentials(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public void PopulateStaticLists()
        {
            visibilityStatuses = new List<VisibilityStatus>
            {
                new VisibilityStatus{ IsHidden = false, IsHiddenSTR = "Нет" },
                new VisibilityStatus{ IsHidden = true, IsHiddenSTR = "Да" }
            };
        }
    }
}
