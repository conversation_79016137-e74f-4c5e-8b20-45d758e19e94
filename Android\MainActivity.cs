﻿using Android.App;
using Android.OS;
using Android.Support.V7.App;
using Android.Runtime;
using Android.Widget;
using Android.Support.V4.Widget;
using Android.Views;
using Android.Support.V4.View;
using Android.Content;
using Android.Support.Design.Widget;
using MyStoreCom.Fragments;
using Android.Net;

namespace MyStoreCom
{
    [Activity(Label = "@string/app_name", Theme = "@style/AppTheme", MainLauncher = true)]
    public class MainActivity : AppCompatActivity, NavigationView.IOnNavigationItemSelectedListener
    {
        public int DefaultUserID = 0;
        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            Xamarin.Essentials.Platform.Init(this, savedInstanceState);
            SetContentView(Resource.Layout.activity_main);

            var trans = SupportFragmentManager.BeginTransaction();
            trans.Add(Resource.Id.mainFrame, new Catalog("computers"), "ComputersMainPage");
            Title = "Интернет-магазин";
            trans.Commit();

            //Android.Support.V7.Widget.Toolbar toolbar = FindViewById<Android.Support.V7.Widget.Toolbar>(Resource.Id.toolbar);

            //SetSupportActionBar(toolbar);

            /*  FloatingActionButton fab = FindViewById<FloatingActionButton>(Resource.Id.fab);
  fab.Click += FabOnClick;*/

            //DrawerLayout drawer = FindViewById<DrawerLayout>(Resource.Id.drawer_layout);
            //ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(this, drawer, toolbar, Resource.String.navigation_drawer_open, Resource.String.navigation_drawer_close);
            //drawer.AddDrawerListener(toggle);
            //toggle.SyncState();

            NavigationView navigationView = FindViewById<NavigationView>(Resource.Id.nav_view);
            navigationView.SetNavigationItemSelectedListener(this);
        }

        public override void OnBackPressed() //backwards navigation
        {
            DrawerLayout drawer = FindViewById<DrawerLayout>(Resource.Id.drawer_layout);
            if (drawer.IsDrawerOpen(GravityCompat.Start))
            {
                drawer.CloseDrawer(GravityCompat.Start);
            }
            else
            {
                base.OnBackPressed();
            }
        }

        public override bool OnCreateOptionsMenu(IMenu menu)
        {
            MenuInflater.Inflate(Resource.Menu.menu_main, menu);
            return true;
        }

        public override bool OnOptionsItemSelected(IMenuItem item)
        {
            int id = item.ItemId;
            if (id == Resource.Id.action_search)
            {
                return true;
            }
            return base.OnOptionsItemSelected(item);
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Android.Content.PM.Permission[] grantResults)
        {
            Xamarin.Essentials.Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }

        public bool OnNavigationItemSelected(IMenuItem item)
        {
            int id = item.ItemId;
            var trans = SupportFragmentManager.BeginTransaction();
            switch (id)
            {
                case (Resource.Id.nav_computers):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("computers"), "Catalog_computers");
                    Title = "Каталог > Компьютеры";
                    break;
                case (Resource.Id.nav_monoblocks):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("monoblocks"), "Catalog_monoblocks");
                    Title = "Каталог > Моноблоки";
                    break;
                case (Resource.Id.nav_laptops):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("laptops"), "Catalog_laptops");
                    Title = "Каталог > Ноутбуки";
                    break;
                case (Resource.Id.nav_smartphones):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("smartphones"), "Catalog_smartphones");
                    Title = "Каталог > Смартфоны";
                    break;
                case (Resource.Id.nav_tablets):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("tablets"), "Catalog_tablets");
                    Title = "Каталог > Планшеты";
                    break;
                case (Resource.Id.nav_other):
                    trans.Replace(Resource.Id.mainFrame, new Catalog("other"), "Catalog_other"); //nav_other
                    Title = "Каталог > Разное";
                    break;
                case (Resource.Id.nav_cart):
                    trans.Replace(Resource.Id.mainFrame, new Cart(), "CartOfUser");
                    Title = "Корзина";
                    break;
                case (Resource.Id.nav_orders):
                    trans.Replace(Resource.Id.mainFrame, new Orders(), "OrdersPage");
                    Title = "Заказы";
                    break;
                case (Resource.Id.nav_search):
                    trans.Replace(Resource.Id.mainFrame, new Search(null), "Search");
                    Title = "Поиск";
                    break;
                case (Resource.Id.nav_signin):
                    trans.Replace(Resource.Id.mainFrame, new Login(), "SignIn");
                    Title = "Авторизация";
                    break;
                case (Resource.Id.nav_map): // Open 2gis. Must be installed!
                    Uri uri = Uri.Parse("dgis://2gis.ru/bratsk/search/dns?queryState=center%2F101.621304%2C56.162138%2Fzoom%2F13");
                    Intent intent = new Intent(Intent.ActionView, uri);
                    intent.SetPackage("ru.dublgis.dgismobile"); // Если не планируете работать с публичной бета-версией, эту строчку надо указать
                    this.StartActivity(intent);
                    //Title = "Наши магазины";
                    break;
            };
            DrawerLayout drawer = FindViewById<DrawerLayout>(Resource.Id.drawer_layout);
            drawer.CloseDrawer(GravityCompat.Start);
            trans.Commit();
            return true;
        }
    }
}