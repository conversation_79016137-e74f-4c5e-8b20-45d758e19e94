<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="110sp">

    <RelativeLayout
        android:id="@+id/cart_item_foreground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white">
        <ImageView
            android:layout_width="90dp"
            android:layout_height="100dp"
            android:minWidth="50px"
            android:minHeight="70px"
            android:id="@+id/image_in_cart"
            android:padding="10dp"
            android:paddingLeft="5dp"
            android:paddingRight="10dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"/>
        <TextView
            android:text="Small Text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/title_cart_item"
            android:textSize="16dp"
            android:textColor="@android:color/black"
            android:fontFamily="sans-serif"
            android:layout_toRightOf="@id/image_in_cart" />
        <TextView
            android:layout_toRightOf="@id/image_in_cart"
            android:layout_below="@id/title_cart_item"
            android:textSize="18dp"
            android:textColor="@android:color/black"
            android:textStyle="bold"
            android:text="Test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/price_cart_item" />
        <TextView
            android:layout_below="@id/price_cart_item"
            android:layout_centerHorizontal="true"
            android:text="2"
            android:paddingTop="10dp"
            android:textSize="16dp"
            android:paddingHorizontal="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/textView_countEach" />
        <Button
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:text="-"
            android:layout_toLeftOf="@id/textView_countEach"
            android:layout_below="@id/price_cart_item"
            android:id="@+id/button_minus_one_item" />
        <Button
            android:layout_below="@id/price_cart_item"
            android:layout_toRightOf="@id/textView_countEach"
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:text="+"
            android:id="@+id/button_plus_one_item"/>
    </RelativeLayout>
</FrameLayout>
