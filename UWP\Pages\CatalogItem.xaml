﻿<Page
    x:Class="MyStoreCOM.Pages.CatalogItem"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:animations="using:Microsoft.Toolkit.Uwp.UI.Animations"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="ButtonBackground" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="{ThemeResource SystemAccentColorLight1}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="{ThemeResource SystemAccentColorDark1}"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>
    </Page.Resources>
    <Grid Margin="20,30,20,20">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <FlipView Grid.Column="0" Width="400" Height="400" Margin="20,0,20,0" VerticalAlignment="Top" Background="Transparent">
                <controls:ImageEx x:Name="image1" />
                <controls:ImageEx x:Name="image2" />
                <controls:ImageEx x:Name="image3" />
            </FlipView>
            
            <RelativePanel Grid.Column="1" Width="600">
                <TextBlock x:Name="Title" Text="HUAWEI Mate 20" TextWrapping="Wrap" FontWeight="Bold" FontSize="32" Style="{StaticResource SubtitleTextBlockStyle}" animations:Connected.Key="MainImage"/>
                <TextBlock x:Name="Price" Text="45 214 Р" Margin="0,50,0,0" FontWeight="Bold" FontSize="30" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="Title" RelativePanel.AlignRightWith="Title"/>
                <TextBlock x:Name="TovarKodField" Text="Код товара:" FontSize="24" Margin="0,0,3,10" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="addtocartBTN"/>
                <TextBlock x:Name="TovarKod" Text="2004" FontSize="24" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="addtocartBTN" RelativePanel.RightOf="TovarKodField"/>
                <TextBlock x:Name="WarrantyField" Text="Гарантия:" FontSize="24" Margin="0,0,3,10" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="TovarKodField"/>
                <TextBlock x:Name="Warranty" Text="2004" FontSize="24" Margin="0,0,3,0" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="TovarKodField" RelativePanel.RightOf="WarrantyField"/>
                <TextBlock x:Name="ManufacturerField" Text="Производитель:" FontSize="24" Margin="0,0,3,0" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="WarrantyField"/>
                <TextBlock x:Name="Manufacturer" Text="2004" FontSize="24" Margin="0,0,3,0" Style="{StaticResource SubtitleTextBlockStyle}" RelativePanel.Below="WarrantyField" RelativePanel.RightOf="ManufacturerField"/>
                <Button x:Name="addtocartBTN" RelativePanel.Below="Price" Margin="0,10,0,20" RelativePanel.AlignRightWith="Price" Foreground="White" Click="AddtocartBTN_Click">
                    <StackPanel>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE7BF;"/>
                        <TextBlock Text="Добавить в корзину"/>
                    </StackPanel>
                </Button>
                <TextBlock x:Name="zz" Text="" TextWrapping="Wrap" Style="{StaticResource SubtitleTextBlockStyle}"/>
            </RelativePanel>
        </Grid>
    </Grid>
</Page>
