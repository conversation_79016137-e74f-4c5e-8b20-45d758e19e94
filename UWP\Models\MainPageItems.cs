﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models
{
    /// <summary>
    /// Class for storing elements on the mainpage
    /// </summary>
    public class Elements
    {
        public string Icon { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
    }

    public class ElementsManager
    {
        public static List<Elements> GetElements()
        {
            var element = new List<Elements>();
            element.Add(new Elements { Icon = "\xEF58", Title = "Пользователи", Description = "Создание, изменение, восстановление учетной записи сотрудника" });
            element.Add(new Elements { Icon = "\xE779", Title = "Информация о сотрудниках", Description = "Полное имя, дата рождения, место жительство, телефон, семейное положение, оклад" });
            element.Add(new Elements { Icon = "\xE128", Title = "Наши магазины", Description = "Расположение магазинов торговой сети на карте, адреса, номера горячей линии торговых точек" });
            element.Add(new Elements { Icon = "\xE902", Title = "Покупатели", Description = "ФИО покупателей, логин, адрес электронной почты, дата регистрации" });
            return element;
        }
    }
}
