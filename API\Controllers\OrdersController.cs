﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using MyStoreCOM_API_database.Models;
using MyStoreCOM_API_database.Services;
using System.Configuration;

namespace MyStoreCOM_API_database.Controllers
{
    [RoutePrefix("Orders")]
    public class OrdersController : ApiController
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;

        private OrdersEmpRepository _orderEmpRepository = new OrdersEmpRepository();

        [Route("all")]
        public IEnumerable<Order> Get(int userid)
        {
            List<Order> Orders = new List<Order>();
            string GetOrdersId = "USE MyStoreCOM SELECT [order_id],[date_created],[statusid] FROM Orders where[user_id] = '" + userid + "' and [ishidden] = '0'";

            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                conn.Open();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = GetOrdersId;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var ordermain = new Order();
                                ordermain.OrderNumber = reader.GetInt32(0);
                                ordermain.OrderDate = Convert.ToString(reader.GetDateTime(1));
                                ordermain.StatusID = reader.GetInt32(2).ToString();
                                string GetItemsInOrder = "use MyStoreCOM SELECT [StoreCatalog].[naimenovenie], ItemsInOrder.[tovar_kod], ItemsInOrder.price, ItemsInOrder.[count], StoreCatalog.warranty FROM ItemsInOrder join StoreCatalog on ItemsInOrder.tovar_kod like StoreCatalog.kod_tovara where ItemsInOrder.order_id like " + ordermain.OrderNumber + "";

                                ///Collection of items in the current order
                                ordermain.SubItemsList = new List<OrderSub>();

                                using (SqlConnection conn2 = new SqlConnection(ConnectionString))
                                {
                                    conn2.Open();
                                    using (SqlCommand cmd2 = conn2.CreateCommand())
                                    {
                                        cmd2.CommandText = GetItemsInOrder;
                                        using (SqlDataReader reader2 = cmd2.ExecuteReader())
                                        {
                                            while (reader2.Read())
                                            {
                                                var item = new OrderSub()
                                                {
                                                    Title = reader2.GetString(0),
                                                    TovarKod = reader2.GetInt32(1),
                                                    Price = reader2.GetInt32(2),
                                                    Count = reader2.GetInt32(3),
                                                    Warranty = reader2.GetInt32(4)
                                                };
                                                item.PriceCountEach = item.Price * item.Count;
                                                ordermain.SubItemsList.Add(item);
                                            }
                                        }
                                    }
                                    conn2.Close();
                                }
                                Orders.Add(ordermain);
                            }
                        }
                    }
                }
            }
            return Orders;
        }

        public HttpResponseMessage Post(int userid)
        {
            SqlConnection con = new SqlConnection(ConnectionString);
            con.Open();
            SqlCommand cmd = new SqlCommand("CreateOrder", con);
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@userID", userid));
            cmd.ExecuteNonQuery();
            con.Close();
            return Request.CreateResponse(HttpStatusCode.Created, userid);
        }

       /* [Route("delete/{orderid:int}")]
        public async Task<HttpResponseMessage> Put(int orderID) // Hide order from user
        {
            SqlConnection con = new SqlConnection(ConnectionString);
            await con.OpenAsync();
            string cmdUpdate = "use MyStoreCom Update Orders SET ishidden='1' where [order_id] like " + orderID + "";
            SqlCommand cmd2 = new SqlCommand(cmdUpdate, con);
            await cmd2.ExecuteNonQueryAsync();
            return Request.CreateResponse(HttpStatusCode.OK);
        }*/

        [Route("delete/{orderid:int}")]
        public HttpResponseMessage Delete(int OrderId) // hide the order from the user
        {
            string cmdText = @"Use MyStoreCom UPDATE Orders SET ishidden = 1 WHERE [order_id] like " + OrderId;
            SqlConnection conn = new SqlConnection(ConnectionString);
            conn.Open();
            SqlCommand cmd = new SqlCommand(cmdText, conn);
            cmd.ExecuteNonQuery();
            conn.Close();
            return Request.CreateResponse(HttpStatusCode.NoContent);
        }

        // ** Employees section starts from here **

        [Route("~/employee/orders/all")]
        public async Task<IEnumerable<OrdersEmp>> Get()
        {
            List<OrdersEmp> _OrdersEmp = new List<OrdersEmp>();
            string GetOrdersId = "USE MyStoreCOM SELECT [order_id],[user_id],[date_created],[ishidden],[statusid],[status_changed] FROM Orders WHERE deletedcompletly like 0";

            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                await conn.OpenAsync();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = GetOrdersId;
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var item = new OrdersEmp()
                                {
                                    OrderNumber = reader.GetInt32(0),
                                    UserID = reader.GetInt32(1),
                                    OrderDate = reader.GetDateTime(2).ToString(),
                                    IsHidden = reader.GetBoolean(3),
                                    StatusID = reader.GetInt32(4),
                                    StatusChanged = reader.GetDateTime(5).ToString()
                                };
                                _OrdersEmp.Add(item);
                            }
                        }
                        conn.Close();
                    }
                }
            }
            return _OrdersEmp;
        }

        [Route("~/employee/order/{id:int}/status")]
        [HttpPut]
        public async Task<HttpResponseMessage> UpdateOrder(int Id, [FromBody]int statusid) // ID == order_ID
        {
            if (await _orderEmpRepository.UpdateOrder(Id, statusid))
            {
                return Request.CreateResponse(HttpStatusCode.OK);
            }
            else
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError);
            }
        }

        [Route("~/employee/order/{orderid:int}")]
        [HttpGet]
        public async Task<IEnumerable<OrderInfoItemsEmp>> GetItemsInOrderEMP(int orderid)
        {
            return await _orderEmpRepository.GetOrderItemsEmp(orderid);
        }

        [Route("~/employee/order/delete/{orderid:int}")]
        [HttpDelete]
        public async Task<HttpResponseMessage> DeleteCompletly(int orderid)
        {
            string cmdText = @"Use MyStoreCom UPDATE Orders SET deletedcompletly = 1 WHERE [order_id] like " + orderid + "";
            SqlConnection conn = new SqlConnection(ConnectionString);
            conn.Open();
            SqlCommand cmd = new SqlCommand(cmdText, conn);
            cmd.ExecuteNonQuery();
            conn.Close();
            return Request.CreateResponse(HttpStatusCode.NoContent);
        }
    }
}
