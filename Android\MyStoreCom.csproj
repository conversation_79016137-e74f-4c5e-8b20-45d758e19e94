<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-android</TargetFramework>
    <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
    <OutputType>Exe</OutputType>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationId>com.mystore.mystore</ApplicationId>
    <ApplicationVersion>1</ApplicationVersion>
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <AndroidUseSharedRuntime>True</AndroidUseSharedRuntime>
    <AndroidLinkMode>None</AndroidLinkMode>
    <EmbedAssembliesIntoApk>False</EmbedAssembliesIntoApk>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AndroidUseSharedRuntime>False</AndroidUseSharedRuntime>
    <AndroidLinkMode>SdkOnly</AndroidLinkMode>
    <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
    <RunAOTCompilation>false</RunAOTCompilation>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Maui.Essentials" Version="8.0.3" />
    <PackageReference Include="Xamarin.AndroidX.AppCompat" Version="1.6.1.5" />
    <PackageReference Include="Xamarin.AndroidX.Core" Version="1.12.0.2" />
    <PackageReference Include="Xamarin.AndroidX.Browser" Version="1.7.0.2" />
    <PackageReference Include="Xamarin.Google.Android.Material" Version="1.11.0.1" />
    <PackageReference Include="Xamarin.AndroidX.RecyclerView" Version="1.3.2.2" />
    <PackageReference Include="FFImageLoading.Maui" Version="1.0.5" />
    <PackageReference Include="Xamarin.GooglePlayServices.Vision" Version="120.2.0.1" />
  </ItemGroup>

</Project>
