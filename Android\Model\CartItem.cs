﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;

namespace MyStoreCom.Model
{
    public class CartItem
    {
        public string Title { get; set; }
        public int Price { get; set; }
        public string PriceSTR { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int PriceCountEach { get; set; }
        public string PriceCountEachSTR { get { return PriceCountEach.ToString("## ### ₽"); } set { PriceCountEach = Convert.ToInt32(value); } }
        public int TovarCod { get; set; }
        public int Nalichie { get; set; }
        public Bitmap Image { get; set; }
        public int EachHas { get; set; }
    }
}