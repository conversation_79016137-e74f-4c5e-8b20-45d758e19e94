﻿<?xml version="1.0" encoding="utf-8" ?>
<!--For all properties see: http://developer.android.com/guide/topics/resources/menu-resource.html-->
<menu xmlns:android="http://schemas.android.com/apk/res/android">

  <group android:checkableBehavior="single">
    <item
        android:id="@+id/nav_orders"
        android:icon="@drawable/baseline_shop_two_24"
        android:title="Заказы" />
    <item
        android:id="@+id/nav_cart"
        android:icon="@drawable/baseline_shopping_cart_24"
        android:title="Корзина" />
  </group>
  <item android:title="Каталог">
    <menu>
      <group
         android:id="@+id/mainmenu"
         android:checkableBehavior="single">
        <item
            android:id="@+id/nav_computers"
            android:icon="@drawable/desktop_tower"
            android:title="Компьютеры" />
        <item
            android:id="@+id/nav_monoblocks"
            android:icon="@drawable/baseline_desktop_mac_24"
            android:title="Моноблоки"/>
        <item
            android:id="@+id/nav_laptops"
            android:icon="@drawable/baseline_laptop_windows_24"
            android:title="Ноутбуки"/>
        <item
            android:id="@+id/nav_smartphones"
            android:icon="@drawable/baseline_smartphone_24"
            android:title="Смартфоны"/>
        <item
            android:id="@+id/nav_tablets"
            android:icon="@drawable/baseline_tablet_24"
            android:title="Планшеты"/>
        <item
            android:id="@+id/nav_other"
            android:icon="@drawable/dots_horizontal_circle_outline"
            android:title="Разное"/>
      </group>
    </menu>
  </item>
      <group
      android:id="@+id/group_search_and_map"
      android:checkableBehavior="single">
        <item
            android:checkable ="true"
            android:id="@+id/nav_search"
            android:icon="@drawable/magnify"
            android:title="Поиск"/>
        <item
            android:checkable="false"
            android:id="@+id/nav_map"
            android:icon="@drawable/map_search"
            android:title="Наши магазины"/>
      </group>
  <group
  android:id="@+id/group_signin"
  android:checkableBehavior="single">
    <item
        android:checkable ="true"
        android:id="@+id/nav_signin"
        android:icon="@drawable/login"
        android:title="Вход"/>
  </group>
</menu>