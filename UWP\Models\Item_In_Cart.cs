﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models
{
    public class Item_In_Cart
    {
        public int IndexNumber { get; set; }
        public string Title { get; set; }
        public int Price { get; set; }
        public string PriceSTR { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int PriceCountEach { get; set; }
        public string PriceCountEachSTR { get { return PriceCountEach.ToString("## ### ₽"); } set { PriceCountEach = Convert.ToInt32(value); } }
        public int TovarCod { get; set; }
        public int Nalichie { get; set; }
        public string Image_URL { get { return (App.Current as App).DefauldAPI_URL + "Content/Images/Catalog/" + TovarCod + ".jpg"; } }
        public int EachHas { get; set; }
        public string EachHasSTR { get { return EachHas.ToString("### шт'.'"); } }
    }
}