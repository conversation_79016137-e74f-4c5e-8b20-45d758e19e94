<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:layout_gravity="top"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20dp"
            android:textColor="@android:color/black"
            android:id="@+id/iteminfo_title"
            android:paddingLeft="10sp"
            android:paddingTop="15sp"
            android:textStyle="bold"
            android:layout_alignParentTop="true"/>
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="200sp"
            android:id="@+id/iteminfo_image"
            android:layout_below="@id/iteminfo_title"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/iteminfo_itemskod"
            android:textSize="18sp"
            android:paddingLeft="10sp"
            android:layout_below="@id/iteminfo_image"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/iteminfo_price"
            android:layout_below="@id/iteminfo_image"
            android:textSize="30sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:paddingRight="10sp"
            android:layout_alignParentRight="true"/>
        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:id="@+id/catalogiteminfo_add_to_cartBTN"
            android:drawableLeft="@drawable/baseline_add_shopping_cart_24"
            android:drawableTint="@android:color/white"
            android:text="Добавить в корзину"
            android:padding="10sp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="10sp"
            android:background="#00aa3e"
            android:textColor="@android:color/white"
            android:layout_below="@id/iteminfo_price"
            android:layout_alignParentRight="true"/>
    </RelativeLayout>
</LinearLayout>

