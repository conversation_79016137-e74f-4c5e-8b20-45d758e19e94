﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.Models;
using MyStoreCOM.ViewModels;
using Microsoft.Toolkit.Uwp.Helpers;
using System.Threading.Tasks;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class CartPage : Page
    {
        /// <summary>
        /// Binding UI to the project's data
        /// </summary>
        public CartViewModel Cart { get; } = new CartViewModel();

        public CartPage()
        {
            this.InitializeComponent();
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Cart.IsLoading = true; });
            Cart.TotalPrice = 0;
            int indx = 0;
            foreach (var item in await Cart.LoadItems())
            {
                item.IndexNumber = indx;
                Cart.Items_in_Cart.Add(item);
                Cart.TotalPrice += item.Price * item.EachHas; //final price box in the right corner
                indx++;
            }
            var count = Cart.Items_in_Cart.Count;
            if (count != 0)
            {
                Cart.TotalPriceSTR = Cart.TotalPrice.ToString("## ### ₽");
                MainCartGrid.Visibility = Visibility.Visible;
            }
            else
            {
                MainCartGrid.Visibility = Visibility.Collapsed;
                EmptyCartLayout.Visibility = Visibility.Visible;
            }

            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Cart.IsLoading = false; });

        }

        private async void Remove_from_cart_Click(object sender, RoutedEventArgs e)
        {
            var btn = (Button)sender;
            int tovarcod = Convert.ToInt32(btn.Tag);
            if (await Cart.DeleteItem(tovarcod)) // successful
            {
                if (Cart.Items_in_Cart.Count == 0)
                {
                    EmptyCartLayout.Visibility = Visibility.Visible;
                    MainCartGrid.Visibility = Visibility.Collapsed;
                }

            }
        }

        private async void CreateOrder_Click(object sender, RoutedEventArgs e)
        {
            LoadingControl.IsLoading = true;
            Task waiting = Task.Delay(3000);
            bool IsSuccess = await Cart.CreateOrder();
            await waiting;
            if (IsSuccess)
            {
                EmptyCartLayout.Visibility = Visibility.Visible;
                MainCartGrid.Visibility = Visibility.Collapsed;
            }
            LoadingControl.IsLoading = false;
        }

        private void Grid_VIEW_ItemClick(object sender, ItemClickEventArgs e)
        {
            var clickeditem = e.ClickedItem;
            var animation = Grid_VIEW.PrepareConnectedAnimation("ca1", e.ClickedItem, "Title");
            Frame.Navigate(typeof(CatalogItem), clickeditem);
        }
    }
}
