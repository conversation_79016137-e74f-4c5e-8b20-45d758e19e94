﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using MyStoreCOM_API_database.Models;
using MyStoreCOM_API_database.Services;

namespace MyStoreCOM_API_database.Controllers
{
    [RoutePrefix("search")]
    public class SearchController : ApiController
    {
        private SearchRepository _searchRepository = new SearchRepository();

        public async Task<IHttpActionResult> GetSearchResults (string barcode)
        {
            var FoundItem = await _searchRepository.SearchByBarcode(barcode);
            if (FoundItem == null)
            {
                return NotFound();
            }
            return Ok(FoundItem);
        }
    }
}
