﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using MyStoreCOM_API_database.Models;

namespace MyStoreCOM_API_database.Services
{
    public class CartRepository
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;

        public async Task<List<Cartitem>> GetCartitems(int user_id)
        {
            string GetAll = @"use MyStoreCOM SELECT [StoreCatalog].[kod_tovara],[naimenovenie],[price],[nalichie],[Basket].[count] FROM StoreCatalog Join Basket on StoreCatalog.kod_tovara like Basket.kod_tovara where [Basket].[user_id] like " + user_id + "";
            List<Cartitem> _cartitems = new List<Cartitem>();
            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                await conn.OpenAsync();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = GetAll;
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var item = new Cartitem
                                {
                                    TovarCod = reader.GetInt32(0),
                                    Title = reader.GetString(1),
                                    Price = reader.GetInt32(2),
                                    Nalichie = reader.GetInt32(3),
                                    EachHas = reader.GetByte(4), // quantity of each item in the basket
                                    Image_URL = "http://mystorecom.ecsogy.ru/20000a.jpg"
                                };
                                item.PriceCountEach = item.Price * item.EachHas;
                                _cartitems.Add(item);
                            }
                        }
                    }
                }
                conn.Close();
            }
            return _cartitems;
        }

        public async Task<HttpResponseMessage> AddToCart(int user_id, int cod_tovara, HttpRequestMessage request)
        {
            byte checkCount = 0;
            SqlConnection con = new SqlConnection(ConnectionString);
            await con.OpenAsync();
            SqlCommand cmd = con.CreateCommand();
            cmd.CommandText = "use MyStoreCom select [count] from basket where [kod_tovara] like " + cod_tovara + " AND [user_id] like " + user_id + "";
            using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
            {
                while (await reader.ReadAsync())
                {
                    checkCount = reader.GetByte(0);
                }
            }
            cmd.Cancel();
            if (checkCount != 0)
            {
                string cmdUpdate = "use MyStoreCom Update Basket SET count= " + (++checkCount) + " where [user_id] like '" + user_id + "%' AND [kod_tovara] like " + cod_tovara + "";
                SqlCommand cmd2 = new SqlCommand(cmdUpdate, con);
                await cmd2.ExecuteNonQueryAsync();
            }
            else
            {
                SqlCommand cmd2 = new SqlCommand("use MyStoreCom Insert into Basket (user_id, kod_tovara, count) values ('" + user_id + "'," + cod_tovara + ",1)", con);
                await cmd2.ExecuteNonQueryAsync();
            }
            con.Close();

            return request.CreateResponse(HttpStatusCode.Created);
        }

        public async Task<HttpResponseMessage> DeleteItemFromCart(int user_id, int cod_tovara, HttpRequestMessage request)
        {
            string cmdText = @"Use MyStoreCOM Delete from Basket where [user_id] like " + user_id + " and [kod_tovara] like " + cod_tovara + "";
            SqlConnection conn = new SqlConnection(ConnectionString);
            await conn.OpenAsync();
            SqlCommand cmd = new SqlCommand(cmdText, conn);
            await cmd.ExecuteNonQueryAsync();
            conn.Close();
            return request.CreateResponse(HttpStatusCode.NoContent);
        }
    }
}