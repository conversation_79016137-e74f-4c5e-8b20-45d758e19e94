﻿using MyStoreCOM.Models;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Windows.Web.Http;
using System;
using Newtonsoft.Json;
using System.Windows.Input;
using Microsoft.Toolkit.Uwp.Helpers;
using Windows.UI.Popups;
using MyStoreCOM.Models.Emloyee;
using System.Collections.Generic;

namespace MyStoreCOM.ViewModels
{
    public class OrdersPageViewModel : BindableBase
    {
        public ObservableCollection<OrderMain> Ordersmain = new ObservableCollection<OrderMain>();

        static readonly HttpClient client = new HttpClient();

        private bool _isupdating;
        public bool IsUpdating
        {
            get => _isupdating;
            set => Set(ref _isupdating, value);
        }

        private bool _isloading;
        public bool IsLoading
        {
            get => _isloading;
            set => Set(ref _isloading, value);
        }

        public List<OrderStatus> OrderStatuses;

        public async Task<bool> LoadOrders()
        {
            try
            {
                Uri uri = new Uri("" + (App.Current as App).DefauldAPI_URL + "Orders/all?userid=" + (App.Current as App).Current_UserID + "");
                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                responseMessage.EnsureSuccessStatusCode();
                var jsonResponse = await client.GetStringAsync(uri);
                var _orders = JsonConvert.DeserializeObject<ObservableCollection<OrderMain>>(jsonResponse);
                Ordersmain.Clear();
                foreach (var item in _orders)
                {
                    //item.Count = i;
                    int StatusID = Convert.ToInt32(item.StatusID.Trim());
                    switch (StatusID) // find the string value of statusid
                    {
                        case 1:
                            item.StatusID = OrderStatuses[0].Status;
                            break;
                        case 2:
                            item.StatusID = OrderStatuses[1].Status;
                            break;
                    }
                    Ordersmain.Add(item);
                    //i++;
                }

                return true;
            }
            catch
            {
                var message = new MessageDialog("Проверьте подключение к интернету");
                await message.ShowAsync();
                return false;
            }
        }

        public async Task DeleteOrder(int ordernum) // hide order from user only
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = true; }); // change the visibility of PrograssBar (Ring)
            int indx = 0;
            foreach (var item in Ordersmain)
            {
                if (item.OrderNumber == ordernum)
                {
                    Ordersmain.RemoveAt(indx);
                    Uri uri = new Uri("https://mystorecomapi.ecsogy.ru/orders/delete/" + ordernum + "");
                    var response = await client.DeleteAsync(uri);
                    response.EnsureSuccessStatusCode();

                    if (response.IsSuccessStatusCode)
                    {
                        //await LoadOrders();
                    }
                    break;
                }
                indx++;
            }
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsUpdating = false; }); // change the visibility of PrograssBar (Ring)
        }
    }
}
