﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models;
using System.Collections.ObjectModel;
using System.Data.SqlClient;

namespace MyStoreCOM.ViewModels
{
    public class UsersPageViewModel
    {
        public ObservableCollection<User> Users { get; private set; } = new ObservableCollection<User>();
        /// <summary>
        /// Query for receiving all the items from the USERS table
        /// </summary>
        public string GetAll { get; } = @"SELECT [username],[password],[First_name],[Second_name],[Middle_name],[AccountDate],[IsActive]FROM[MyStoreCOM].[dbo].[users]";

        public async void LoadUsers(string query)
        {
            Users.Clear();

            using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
            {
                await conn.OpenAsync();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = query;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            int i = 1;
                            while (reader.Read())
                            {
                                var user = new User
                                {
                                    Count = i,
                                    Login = reader.GetString(0),
                                    Password = reader.GetString(1),
                                    FirstName = reader.GetString(2),
                                    SecondName = reader.GetString(3),
                                    MiddleName = reader.GetString(4),
                                    AccountDate = reader.GetDateTime(5),
                                    IsActive = reader.GetBoolean(6)
                                };
                                i++;
                                Users.Add(user);

                            }
                        }
                    }
                }
                conn.Close();
            }
        }
    }
}
