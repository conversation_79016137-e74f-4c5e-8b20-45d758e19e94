﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Media.Animation;
using Windows.UI.Xaml.Navigation;
using Microsoft.Toolkit.Uwp.UI.Animations;
using Windows.ApplicationModel.Core;
using Windows.UI.ViewManagement;
using Windows.UI;
using Windows.Web.Http;
using Windows.Web.Http.Headers;
using Newtonsoft.Json;
using MyStoreCOM.Models;
using System.Threading.Tasks;
using Windows.UI.Popups;
using MyStoreCOM.ViewModels;


// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class SignInPage : Page
    {
        private readonly LoginPageViewModel _LoginViewModel = new LoginPageViewModel();
        public SignInPage()
        {
            this.InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            CoreApplication.GetCurrentView().TitleBar.ExtendViewIntoTitleBar = true;
            ///remove the solid-colored backgrounds behind the caption controls and system back button
            ApplicationViewTitleBar titleBar = ApplicationView.GetForCurrentView().TitleBar;
            titleBar.ButtonBackgroundColor = Colors.Transparent;
            titleBar.ButtonInactiveBackgroundColor = Colors.Transparent;
            titleBar.ButtonForegroundColor = Colors.White;
            //await backgroundIMG.Scale(centerX: 0f, centerY: 0f, scaleX: 1.2f, scaleY: 1.2f, duration: 10000, delay: 0).StartAsync();

        }

        private async void SignInButton_Click(object sender, RoutedEventArgs e)
        {
            LoadingControl.IsLoading = true;
            bool IsSuccessful = await _LoginViewModel.SignIn(UsernameTextBox.Text.Trim(), PasswordTextBox.Password.Trim());
            if (!IsSuccessful)
            {
                LoadingControl.IsLoading = false;
            } else
            {
                Frame.Navigate(typeof(ApplicationShell), "logged", new DrillInNavigationTransitionInfo());
            }

        }

        private async void SignUpBTN_Click(object sender, RoutedEventArgs e)
        {
            LoadingControl.IsLoading = true;
            bool IsSuccessful = await _LoginViewModel.SignUp(UsernameTextBox2.Text.Trim(), PasswordTextBox2.Password.Trim(), PasswordTextBox2.Password.Trim());
            if (!IsSuccessful)
            {
                LoadingControl.IsLoading = false;
            }
            else
            {
                Frame.Navigate(typeof(ApplicationShell), "logged", new DrillInNavigationTransitionInfo());
            }         
        }

        private void Credentials_KeyUp(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == Windows.System.VirtualKey.Enter)
            {
                SignInButton_Click(this, new RoutedEventArgs());
            }
        }

        private void UsernameTextBox2_KeyUp(object sender, KeyRoutedEventArgs e)
        {
            if (e.Key == Windows.System.VirtualKey.Enter)
            {
                SignUpBTN_Click(this, new RoutedEventArgs());
            }
        }
    }
}
