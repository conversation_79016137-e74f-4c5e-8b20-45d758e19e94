﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using Android.Support.V4.App;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;
using Android.Preferences;

namespace MyStoreCom.Fragments
{
    public class Login : Android.Support.V4.App.Fragment
    {
        View myFragmentView;

        readonly Configuration config = new Configuration();

        HttpClient client = new HttpClient();

        string UserLogin, Password;

        EditText login_box, passsword_box;

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Create your fragment here
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            myFragmentView = inflater.Inflate(Resource.Layout.login, container, false);
            login_box = myFragmentView.FindViewById<EditText>(Resource.Id.signin_login);
            passsword_box = myFragmentView.FindViewById<EditText>(Resource.Id.signin_password);
            Button signinBTN = myFragmentView.FindViewById<Button>(Resource.Id.btn_signin);
            signinBTN.Click += SigninBTN_Click;

            return myFragmentView;
        }

        private async void SigninBTN_Click(object sender, EventArgs e)
        {
            Uri uri = new Uri(config.DefauldAPI_URL + "user/login");
            UserLogin = login_box.Text;
            Password = passsword_box.Text;


            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", EncodeCredentials(UserLogin, Password));
            try
            {
                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var jsonResponse = await client.GetStringAsync(uri);
                    var loginuser = JsonConvert.DeserializeObject<LoginUser>(jsonResponse);

                    ISharedPreferences prefs = PreferenceManager.GetDefaultSharedPreferences(Activity);
                    ISharedPreferencesEditor editor = prefs.Edit();
                    editor.PutInt("user_id", loginuser.UserID);
                    editor.Apply();

                    var fragmentManager = Activity.SupportFragmentManager;
                    var trans = fragmentManager.BeginTransaction();
                    trans.Replace(Resource.Id.mainFrame, new Catalog("computers"), "Catalog_computers");
                    Activity.Title = "Каталог > Компьютеры";
                    trans.Commit();
                }
                else
                {
                    if (responseMessage.StatusCode == HttpStatusCode.NotFound)
                    {
                        Toast.MakeText(this.Activity, "Введенная комбинация имени пользователя и пароля не найдена. \nПовторите попытку еще раз", ToastLength.Long).Show();
                    }
                    else
                    {
                        Toast.MakeText(this.Activity, "Произошла ошибка. Повторите позже", ToastLength.Long).Show();
                    }
                }
            }
            catch
            {
                Toast.MakeText(this.Activity, "Проверьте подключение к интернету", ToastLength.Long).Show();
            }

        }
        private static string EncodeCredentials(string login, string password)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes("" + login + ":" + password + "");
            return System.Convert.ToBase64String(plainTextBytes);
        }
    }
}