﻿using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models
{
    public class OrderMain
    {
        public int Count { get; set; }
        public int OrderNumber { get; set; }
        public string StatusID { get; set; }
        public ObservableCollection<OrderSub> SubItemsList { get; set; }
        public string OrderDate { get; set; }
    }
    public class OrderSub
    {
        public string Title { get; set; }
        public int Price { get; set; }
        public string PriceS { get { return Price.ToString("## ### ₽"); } set { Price = Convert.ToInt32(value); } }
        public int PriceCountEach { get; set; }
        public string TotalPriceCountSTR { get { return PriceCountEach.ToString("## ### ₽"); } }
        public int TovarKod { get; set; }
        public int Count { get; set; }
        public string Image { get { return (App.Current as App).DefauldAPI_URL + "Content/Images/Catalog/" + TovarKod + ".jpg"; } }
        public int Warranty { get; set; }
    }

}
