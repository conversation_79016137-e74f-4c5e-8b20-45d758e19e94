﻿<Page
    x:Class="MyStoreCOM.Pages.Users"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <RelativePanel x:Name="RelativePanel" Grid.Row="0">
            <TextBlock x:Name="Title" Margin="20,0,0,0" Style="{StaticResource TitleTextBlockStyle}" Text="Пользователи" RelativePanel.AlignVerticalCenterWith="CommandBar"/>
            <CommandBar x:Name="CommandBar"
                        DefaultLabelPosition="Right"
                        Background="White"
                        RelativePanel.AlignVerticalCenterWithPanel="True"
                        RelativePanel.AlignRightWithPanel="True">
                <AppBarButton Icon="Refresh" Label="Обновить"/>
                <AppBarButton Icon="Add" Label="Добавить строку"/>
                <AppBarButton Icon="Delete" Label="Удалить"/>
            </CommandBar>
        </RelativePanel>
        <ProgressBar Grid.Row="1" IsIndeterminate="True"/>
        <controls:DataGrid
            Grid.Row="2"
            x:Name="DataGridUsers"
            IsReadOnly="False"
            AlternatingRowForeground="Gray"
            AutoGenerateColumns="False"
            VerticalAlignment="Stretch"
            HorizontalAlignment="Stretch"
            GridLinesVisibility="All"
            ItemsSource="{x:Bind User.Users}">
            <controls:DataGrid.Columns>
                <controls:DataGridTextColumn Header="Логин" Binding="{Binding Login}"/>
                <controls:DataGridTextColumn Header="Пароль" Binding="{Binding Password}"/>
                <controls:DataGridTextColumn Header="Имя" Binding="{Binding FirstName}"/>
                <controls:DataGridTextColumn Header="Отчество" Binding="{Binding MiddleName}"/>
                <controls:DataGridTextColumn Header="Фамилия" Binding="{Binding SecondName}"/>
                <controls:DataGridTextColumn Header="Аккаунт создан" Binding="{Binding AccountDate}"/>
                <controls:DataGridTextColumn Header="Аккаунт активен" Binding="{Binding IsActive}"/>
            </controls:DataGrid.Columns>
        </controls:DataGrid>
    </Grid>
</Page>
