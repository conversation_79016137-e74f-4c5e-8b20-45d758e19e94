<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:cardview="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <android.support.v7.widget.CardView
        android:layout_width="fill_parent"
        android:layout_marginHorizontal="10dp"
        android:layout_marginVertical="10dp"
        android:layout_height="300dp"
        android:layout_gravity="center_horizontal"
        cardview:cardElevation="4dp"
        cardview:cardCornerRadius="10dp">
        <RelativeLayout
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:padding="10dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/orders_parent_numberfiled"
                android:text="Заказ №"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="3dp"
                android:textStyle="bold"
                android:textSize="16dp"
                android:textColor="@android:color/black"
                android:layout_alignParentTop="true"/>
            <TextView
                android:id="@+id/orders_parent_textview"
                android:layout_alignParentTop="true"
                android:layout_toRightOf="@id/orders_parent_numberfiled"
                android:layout_marginTop="10dp"
                android:layout_marginRight="6dp"
                android:text="2"
                android:textStyle="bold"
                android:textSize="16dp"
                android:textColor="@android:color/black"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/ord_between_ordnum_and_orddate"
                android:text="от"
                android:textStyle="bold"
                android:textSize="16dp"
                android:textColor="@android:color/black"
                android:layout_marginTop="10dp"
                android:layout_marginRight="6dp"
                android:layout_toRightOf="@id/orders_parent_textview"/>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/ord_parent_orderdate"
                android:layout_marginTop="10dp"
                android:layout_toRightOf="@id/ord_between_ordnum_and_orddate"
                android:text="от 12.12.1222. 23.41. АМ"
                android:textStyle="bold"
                android:textSize="16dp"
                android:textColor="@android:color/black"/>
            <ImageButton
                android:id="@+id/orders_delete"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:background="@color/colorPrimary"
                android:tint="@android:color/white"
                android:src="@drawable/baseline_delete_24"
                android:layout_alignParentRight="true"/>
            <android.support.v7.widget.RecyclerView
                android:id="@+id/orders_recyclerview_child"
                android:layout_centerInParent="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/orders_parent_textview"
                android:layout_marginTop="10dp" />
        </RelativeLayout>
    </android.support.v7.widget.CardView>

</LinearLayout>
