﻿using Android.Content;
using Android.OS;
using Android.Preferences;
using Android.Support.V4.Widget;
using Android.Support.V7.Widget;
using Android.Support.V7.Widget.Helper;
using Android.Views;
using Android.Widget;
using MyStoreCom.Model;
using MyStoreCom.ViewModels;
using System.Collections.Generic;

namespace MyStoreCom.Fragments
{
    public class Cart : Android.Support.V4.App.Fragment
    {
        View myFragmentView = null;
        private static CartItemsVM _CartVM = new CartItemsVM();
        private static CartItemsAdapter mAdapter;
        RecyclerView.LayoutManager mLayoutManager;
        SwipeRefreshLayout swiper;
        int CountItems, TotalPrice;

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            ISharedPreferences prefs = PreferenceManager.GetDefaultSharedPreferences(Activity);
            _CartVM.UserID = prefs.GetInt("user_id", 0);

            CreatePage();
        }

        private async void Swiper_Refresh(object sender, System.EventArgs e)
        {
            var ListOfItems = await _CartVM.LoadItems();
            mAdapter.NotifyDataSetChanged();
            swiper.Refreshing = false;
        }

        private async void MakeOrder_Click(object sender, System.EventArgs e)
        {
            var succesful = await _CartVM.CreateOrder();
            if (succesful)
            {
                CreatePage();
                Toast.MakeText(this.Activity, "Заказ успешно создан", ToastLength.Short).Show();
            }
            else
            {
                Toast.MakeText(this.Activity, "Не удалось создать заказ. Повторите попытку позже", ToastLength.Short).Show();
            }
        }

        void OnItemClick(object sender, int position)
        {
            int photoNum = position + 1;
            Toast.MakeText(this.Activity, "This is photo number " + photoNum, ToastLength.Short).Show();
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            myFragmentView = inflater.Inflate(Resource.Layout.cart_main, container, false);

            var progressbar = myFragmentView.FindViewById<ProgressBar>(Resource.Id.cart_progress);
            progressbar.Visibility = ViewStates.Gone;

            TextView InCartQuantity = myFragmentView.FindViewById<TextView>(Resource.Id.text_cart_TotalQuantity);
            InCartQuantity.Text = CountItems.ToString("В корзине ## элемент(-ов)");


            return myFragmentView;
        }

        public async void CreatePage()
        {
            var ListOfItems = await _CartVM.LoadItems();
            if (ListOfItems.Count == 0)
            {
                RecyclerView mRecyclerView = myFragmentView.FindViewById<RecyclerView>(Resource.Id.recycleViewCart);
                mRecyclerView.Visibility = ViewStates.Gone;

                RelativeLayout TotalPanel = myFragmentView.FindViewById<RelativeLayout>(Resource.Id.cart_totalPanel);
                TotalPanel.Visibility = ViewStates.Gone;

                TextView EmptyCart = myFragmentView.FindViewById<TextView>(Resource.Id.cart_empty);
                EmptyCart.Visibility = ViewStates.Visible;
            }
            else
            {
                RecyclerView mRecyclerView = myFragmentView.FindViewById<RecyclerView>(Resource.Id.recycleViewCart);

                RelativeLayout TotalPanel = myFragmentView.FindViewById<RelativeLayout>(Resource.Id.cart_totalPanel);
                TotalPanel.Visibility = ViewStates.Visible;

                TextView EmptyCart = myFragmentView.FindViewById<TextView>(Resource.Id.cart_empty);
                EmptyCart.Visibility = ViewStates.Gone;

                mAdapter = new CartItemsAdapter(ListOfItems);
                mRecyclerView.SetAdapter(mAdapter);

                mLayoutManager = new LinearLayoutManager(this.Activity);
                mRecyclerView.SetLayoutManager(mLayoutManager);
                DividerItemDecoration itemDecoration = new DividerItemDecoration(this.Activity, 1);
                mRecyclerView.AddItemDecoration(itemDecoration);

                mAdapter.ItemClick += OnItemClick;

                ItemTouchHelper.Callback callback = new MyItemTouchHelper();
                ItemTouchHelper itemTouchHelper = new ItemTouchHelper(callback);
                itemTouchHelper.AttachToRecyclerView(mRecyclerView);

                Button makeOrder = myFragmentView.FindViewById<Button>(Resource.Id.cart_btn_makeorder);
                makeOrder.Click += MakeOrder_Click;
            }
            swiper = myFragmentView.FindViewById<SwipeRefreshLayout>(Resource.Id.cart_swiperefresh);
            swiper.Refresh += Swiper_Refresh;

        }

        public class CartViewHolder : RecyclerView.ViewHolder
        {
            public ImageView Image { get; private set; }
            public TextView Title { get; private set; }
            public TextView Price { get; private set; }
            public TextView Quantity { get; private set; }
            public Button AddToCart { get; private set; }
            public RelativeLayout viewBackground, viewForeground;
            public CartViewHolder(View itemView, System.Action<int> listener)
                : base(itemView)
            {
                AddToCart = ItemView.FindViewById<Button>(Resource.Id.add_to_cartBTN);                // Locate and cache view references:
                Image = itemView.FindViewById<ImageView>(Resource.Id.image_in_cart);
                Title = itemView.FindViewById<TextView>(Resource.Id.title_cart_item);
                Price = itemView.FindViewById<TextView>(Resource.Id.price_cart_item);
                Quantity = ItemView.FindViewById<TextView>(Resource.Id.textView_countEach);
                // viewBackground = itemView.FindViewById<RelativeLayout>(Resource.Id.car_item_background);
                viewForeground = itemView.FindViewById<RelativeLayout>(Resource.Id.cart_item_foreground);

                // Detect user clicks on the item view and report which item
                // was clicked (by layout position) to the listener:
                itemView.Click += (sender, e) => listener(base.LayoutPosition);
            }
        }

        public class CartItemsAdapter : RecyclerView.Adapter
        {
            // Event handler for item clicks:
            public event System.EventHandler<int> ItemClick;
            public List<CartItem> ListItems;

            public CartItemsAdapter(List<CartItem> listofitems)
            {
                ListItems = listofitems;
            }

            public override RecyclerView.ViewHolder
                OnCreateViewHolder(ViewGroup parent, int viewType)
            {
                // Inflate the CardView for the photo:
                View itemView = LayoutInflater.From(parent.Context).
                            Inflate(Resource.Layout.cart_pattern, parent, false);

                // Create a ViewHolder to find and hold these view references, and 
                // register OnClick with the view holder:
                //itemView.Click += (sender, e) => listener(base.LayoutPosition);

                CartViewHolder vh = new CartViewHolder(itemView, OnClick);
                return vh;
            }

            public override void OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
            {
                CartViewHolder vh = holder as CartViewHolder;

                vh.Image.SetImageBitmap(ListItems[position].Image);
                vh.Title.Text = ListItems[position].Title;
                vh.Price.Text = ListItems[position].PriceSTR;
                vh.Quantity.Text = ListItems[position].EachHas.ToString();
            }

            public override int ItemCount
            {
                get { return ListItems.Count; }
            }

            public void OnClick(int position)
            {
                ItemClick?.Invoke(this, position);
                // consider below!
                 // if (ItemClick != null)
                 // ItemClick(this, position);
            }

            public async void DeleteItem(int position)
            {
                var _deleteItem = _CartVM.DeleteItem(position); // start removing from SQL database
                ListItems.RemoveAt(position); // remove from local collection while removing from SQL
                NotifyItemRemoved(position);
                await _deleteItem;
            }
        }

        public class MyItemTouchHelper : ItemTouchHelper.Callback
        {
            CartItemsVM _cartItemsVM = new CartItemsVM();

            public MyItemTouchHelper()
            {
                // you can pass any thing in your contractor , may be your RecyclerView adapter 
            }
            public override int GetMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder)
            {

                int dragFlags = ItemTouchHelper.Up | ItemTouchHelper.Down;
                int swipeFlags = ItemTouchHelper.Start | ItemTouchHelper.End;
                return MakeMovementFlags(dragFlags, swipeFlags);
            }

            public override bool OnMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target)
            {
                return true;
            }

            public override void OnSwiped(RecyclerView.ViewHolder viewHolder, int direction)
            {
                int item_position = viewHolder.AdapterPosition;
                mAdapter.DeleteItem(item_position);
            }
        }
    }


}