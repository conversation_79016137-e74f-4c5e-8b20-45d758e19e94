﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models;
using System.Data.SqlClient;

namespace MyStoreCOM.Repository.SQL
{
    public class SQLComputerRepository  // : IComputerRepository
    {
        /*    public async Task<IEnumerable<Computer>> GetAsync()
            {
                using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
                {
                    await conn.OpenAsync();
                    if(conn.State == System.Data.ConnectionState.Open)
                    {
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "query";
                        }
                    }
                }
            }
        }*/
    }
}
