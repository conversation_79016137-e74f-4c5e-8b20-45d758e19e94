<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.SwipeRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cart_swiperefresh"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ProgressBar
            android:id="@+id/cart_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateTint="@color/colorPrimary"
            android:layout_centerInParent="true" />
        <TextView
            android:id="@+id/cart_empty"
            android:visibility="gone"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20dp"
            android:text="В корзине ещё ничего нет"
            android:textColor="@android:color/black" />
        <android.support.v7.widget.RecyclerView
            android:id="@+id/recycleViewCart"
            android:scrollbars="vertical"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:layout_above="@+id/cart_totalPanel" />
        <RelativeLayout
            android:id="@+id/cart_totalPanel"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_height="60dp"
            android:background="@android:color/holo_blue_light">
            <View
                android:layout_alignParentTop="true"
                android:layout_height="1dp"
                android:layout_width="fill_parent"
                android:background="@color/cardview_dark_background" />
            <TextView
                android:id="@+id/text_cart_TotalQuantity"
                android:paddingLeft="10dp"
                android:paddingTop="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="В корзине 6 товаров"
                android:visibility="gone"
                android:textColor="@android:color/black"/>
            <TextView
                android:id="@+id/text_cart_TotalPrice"
                android:visibility="gone"
                android:paddingLeft="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="На сумму 89 982 Р"
                android:layout_below="@+id/text_cart_TotalQuantity"
                android:textColor="@android:color/black"/>
            <Button
                android:id="@+id/cart_btn_makeorder"
                android:layout_centerVertical="true"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:text="Заказать"
                android:layout_alignParentRight="true"/>
        </RelativeLayout>
    </RelativeLayout>
</android.support.v4.widget.SwipeRefreshLayout>
