﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;
using System.Threading.Tasks;
using System.Net.Http;
using System.Data.SqlClient;
using MyStoreCOM_API_database.Models;

namespace MyStoreCOM_API_database.Services
{
    /// <summary>
    /// Class for usual customers 
    /// </summary>
    public class OrderRepository
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;
    }
    /// <summary>
    /// Class for authorized employees only. Autorized users are able to change any order
    /// </summary>
    public class OrdersEmpRepository
    {
        public readonly string ConnectionString = ConfigurationManager.ConnectionStrings["MyStoreCom"].ConnectionString;

        public async Task<bool> UpdateOrder(int orderid, int statusid)
        {
            try
            {
                SqlConnection con = new SqlConnection(ConnectionString);
                await con.OpenAsync();
                string updatestr = "use MyStoreCom UPDATE Orders SET status_changed = (getdate()), statusID = " + statusid + " where order_id LIKE " + orderid + "";
                SqlCommand cmd = new SqlCommand(updatestr, con);
                await cmd.ExecuteNonQueryAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<OrderInfoItemsEmp>> GetOrderItemsEmp(int orderid)
        {
            List<OrderInfoItemsEmp> ItemsInOrder = new List<OrderInfoItemsEmp>();
            string UserInfoSQL = "USE MyStoreCOM SELECT [date_created],[statusid],[status_changed],[ishidden],[orders].[user_id], [users].First_name, [users].[Second_name], [users].Middle_name, [users].[phone] FROM Orders JOIN users on[Orders].[user_id] LIKE[users].[user_id] where Orders.order_id LIKE " + orderid + "";
            string ItemsInOrderSQL = "USE MyStoreCOM SELECT [tovar_kod], [ItemsInOrder].[price], [count], [naimenovanie] FROM ItemsInOrder JOIN StoreCatalog on kod_tovara like tovar_kod where order_id like " + orderid + "";
            using (SqlConnection conn = new SqlConnection(ConnectionString))
            {
                await conn.OpenAsync();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = UserInfoSQL;
                        using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var item = new OrderInfoItemsEmp();

                                item.OrderDate = reader.GetDateTime(0).ToString();
                                item.StatusID = reader.GetInt32(1);
                                item.StatusChanged = reader.GetDateTime(2).ToString();
                                item.IsHidden = reader.GetBoolean(3);
                                item.UserID = reader.GetInt32(4);
                                item.UsrFullName = reader.GetString(6).Trim()+ (" ") + reader.GetString(5).Trim()+ (" ") + reader.GetString(7).Trim();
                                item.UsrPhoneNumber = reader.GetString(8);
                                item.ItemsInOrder = new List<OrderItemsEmp>();
                                using (SqlConnection conn2 = new SqlConnection(ConnectionString))
                                {
                                    await conn2.OpenAsync();
                                    if (conn2.State == System.Data.ConnectionState.Open)
                                    {
                                        SqlCommand cmd2 = conn2.CreateCommand();
                                        cmd2.CommandText = ItemsInOrderSQL;
                                        SqlDataReader reader2 = await cmd2.ExecuteReaderAsync();
                                        while (await reader2.ReadAsync())
                                        {
                                            var item2 = new OrderItemsEmp
                                            {
                                                TovarKod = reader2.GetInt32(0),
                                                Price = reader2.GetInt32(1),
                                                Count = reader2.GetInt32(2),
                                                Title = reader2.GetString(3)
                                            };
                                            item.ItemsInOrder.Add(item2); // Second collection. Contains items in order (for emp only page)
                                        }
                                    }
                                }

                                ItemsInOrder.Add(item);
                            }
                        }
                        conn.Close();
                    }
                }
            }
            return ItemsInOrder;
        }
    }
}