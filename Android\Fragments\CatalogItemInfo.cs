﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using FFImageLoading;
using MyStoreCom.Model;
using MyStoreCom.Data;
using MyStoreCom.ViewModels;
using Android.Preferences;

namespace MyStoreCom.Fragments
{
    public class CatalogItemInfo : Android.Support.V4.App.Fragment
    {
        public CatalogItemInfoVM _catalogitemVM = new CatalogItemInfoVM();

        private static readonly Configuration config = new Configuration();

        CatalogItem FoundItem = null;

        View myFragmentView = null;

        public CatalogItemInfo(CatalogItem founditem)
        {
            FoundItem = founditem;
        }

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            ISharedPreferences prefs = PreferenceManager.GetDefaultSharedPreferences(Activity);
            _catalogitemVM.UserID = prefs.GetInt("user_id", 0);
            // Create your fragment here
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            myFragmentView = inflater.Inflate(Resource.Layout.catalog_item, container, false);


            TextView Title = myFragmentView.FindViewById<TextView>(Resource.Id.iteminfo_title);
            Title.Text = FoundItem.Naimenovenie;

            TextView Price = myFragmentView.FindViewById<TextView>(Resource.Id.iteminfo_price);
            Price.Text = FoundItem.PriceSTR;

            TextView TovarKod = myFragmentView.FindViewById<TextView>(Resource.Id.iteminfo_itemskod);
            TovarKod.Text = FoundItem.Kod_tovaraSTR;

            Button AddToCartBTN = myFragmentView.FindViewById<Button>(Resource.Id.catalogiteminfo_add_to_cartBTN);
            AddToCartBTN.Click += delegate
            {
                AddToCartBTN_Click(FoundItem.Kod_tovara, FoundItem.Naimenovenie);
            };

            ImageView MainImage = myFragmentView.FindViewById<ImageView>(Resource.Id.iteminfo_image);
            ImageService.Instance.LoadUrl(config.DefauldAPI_URL + "Content/Images/CatalogLarge/" + FoundItem.Kod_tovara + ".jpg").Into(MainImage);

            return myFragmentView;
        }

        private async void AddToCartBTN_Click(int kodTovara, string title)
        {
            var IsAddedtoCart = await _catalogitemVM.AddToCart(kodTovara);
            if (IsAddedtoCart == System.Net.HttpStatusCode.Created)
            {
                Toast.MakeText(Context, "Элемент " + title + " добавлен в корзину", ToastLength.Short).Show();
            }
            else
            {
                Toast.MakeText(Context, "Не удалось добавить в корзину!", ToastLength.Long).Show();
            }
        }
    }
}