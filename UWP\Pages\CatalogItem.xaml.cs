﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Media.Animation;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.ViewModels;
using MyStoreCOM.Models;
using System.Threading.Tasks;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class CatalogItem : Page
    {
        public CatalogItemVM _catalogItemVM = new CatalogItemVM();

        Computer CurrentCatalogITem = new Computer();

        public CatalogItem()
        {
            this.InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            try
            {
                CurrentCatalogITem = (Computer)e.Parameter;
            }
            catch
            {
                var diffrenetobject = (OrderSub)e.Parameter;
                CurrentCatalogITem.Naimenovenie = diffrenetobject.Title;
                CurrentCatalogITem.Price = diffrenetobject.Price;
                CurrentCatalogITem.Kod_tovara = diffrenetobject.TovarKod;
                CurrentCatalogITem.Warranty = diffrenetobject.Warranty;
                CurrentCatalogITem.Proizvoditel = "";
                ManufacturerField.Visibility = Visibility.Collapsed;
                Manufacturer.Visibility = Visibility.Collapsed;
            }
            Title.Text = CurrentCatalogITem.Naimenovenie;
            Price.Text = CurrentCatalogITem.PriceSTR;
            TovarKod.Text = CurrentCatalogITem.Kod_tovara.ToString();
            Warranty.Text = CurrentCatalogITem.WarrantyS;
            Manufacturer.Text = CurrentCatalogITem.Proizvoditel;
            try
            {
                var animation = ConnectedAnimationService.GetForCurrentView().GetAnimation("ca1");
                animation.TryStart(Title);
            }
            catch
            {

            }
            image1.Source = (App.Current as App).DefauldAPI_URL + "Content/Images/CatalogLarge/" + CurrentCatalogITem.Kod_tovara + ".jpg";
            image2.Source = (App.Current as App).DefauldAPI_URL + "Content/Images/CatalogLarge/" + CurrentCatalogITem.Kod_tovara + "_2.jpg";
            image3.Source = (App.Current as App).DefauldAPI_URL + "Content/Images/CatalogLarge/" + CurrentCatalogITem.Kod_tovara + "_3.jpg";

            _catalogItemVM.ImageURL = (App.Current as App).DefauldAPI_URL + "Content/Images/CatalogLarge/" + CurrentCatalogITem.Kod_tovara + ".jpg";
        }

        private async void AddtocartBTN_Click(object sender, RoutedEventArgs e)
        {
            if ((App.Current as App).Current_UserID == 0)
            {
                ContentDialog NotSignedIn = new ContentDialog
                {
                    Title = "Выполните вход",
                    Content = "Для добавления товаров в корзину и совершения покупок необходимо\nзарегистрироваться, либо выполнить вход под своей учетной записью",
                    CloseButtonText = "Закрыть"
                };
                await NotSignedIn.ShowAsync();
            }
            else await _catalogItemVM.Add_To_CartAsync(CurrentCatalogITem.Kod_tovara);
        }
    }
}
