<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>MyStoreCOM_API_database</RootNamespace>
    <AssemblyName>MyStoreCOM_API_database</AssemblyName>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.WebApiCompatShim" Version="2.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
    <Content Include="Content\**" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="Scripts\**" CopyToOutputDirectory="PreserveNewest" />
    <Content Include="fonts\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Views\**" CopyToOutputDirectory="PreserveNewest" />
    <Content Update="Areas\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
