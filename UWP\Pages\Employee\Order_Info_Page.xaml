﻿<Page
    x:Class="MyStoreCOM.Pages.Employee.Order_Info_Page"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages.Employee"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:data="using:MyStoreCOM.Models.Emloyee"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid Margin="20">
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind _OrderInfoVM.IsLoading, Mode=OneWay}" IsActive="True"/>
        <Button x:Name="RetryBTN" Content="Попробовать ещё раз" Visibility="Collapsed" HorizontalAlignment="Center" VerticalAlignment="Center"/>
        <Grid x:Name="ContentGrid" Visibility="Visible" HorizontalAlignment="Stretch">
            <StackPanel>
                <RelativePanel>
                    <TextBlock x:Name="Header" Text="Заказ №" Style="{StaticResource SubheaderTextBlockStyle}"/>
                    <TextBlock x:Name="Orderid" Style="{StaticResource SubheaderTextBlockStyle}" RelativePanel.RightOf="Header" Margin="5,0,0,0"/>
                </RelativePanel>
                <StackPanel Orientation="Horizontal" Margin="0,12,0,12">
                    <TextBlock Text="Пользователь:"/>
                    <TextBlock x:Name="Username" Text="{x:Bind _OrderInfoVM.UsrFullName}" Margin="3,0,25,0"/>
                    <TextBlock Text="ID пользователя:" />
                    <TextBlock x:Name="Userid" Text="{x:Bind _OrderInfoVM.UserID}" Margin="3,0,25,0"/>
                    <TextBlock Text="Телефон:"/>
                    <TextBlock x:Name="Userphone" Text="{x:Bind _OrderInfoVM.UsrPhoneNumber}" Margin="3,0,0,0"/>
                </StackPanel>
                <ScrollViewer>
                    <Grid Margin="0,12,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="300"/>
                        </Grid.ColumnDefinitions>
                        <ListView ItemsSource="{x:Bind _OrderInfoVM._ItemsInOrder}" 
                          RelativePanel.Below="Header"
                          HorizontalAlignment="Stretch"
                          Margin="0,0,12,0"
                                  Grid.Column="0">
                            <ListView.ItemTemplate>
                                <DataTemplate x:DataType="data:OrderItems">
                                    <RelativePanel HorizontalAlignment="Stretch" Width="auto" Margin="10">
                                        <TextBlock x:Name="Title" Text="{x:Bind Title}" Margin="0,15,10,0" FontSize="18" Style="{StaticResource BaseTextBlockStyle}"/>
                                        <TextBlock x:Name="TovarCodField" Text="Код:" RelativePanel.Below="Title" Margin="0,5,3,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="TovarCod" Text="{x:Bind TovarKod}" RelativePanel.RightOf="TovarCodField" RelativePanel.Below="Title" Margin="0,5,3,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="Price" Text="{x:Bind PriceS}" RelativePanel.LeftOf="CountField" RelativePanel.AlignVerticalCenterWithPanel="True" Margin="0,0,5,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="CountField" Text="x" RelativePanel.LeftOf="Count" RelativePanel.AlignVerticalCenterWithPanel="True" Margin="0,0,5,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="Count" Text="{x:Bind Count}" RelativePanel.AlignHorizontalCenterWithPanel="True" RelativePanel.AlignVerticalCenterWithPanel="True" Margin="0,0,3,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="Pieces" Text="шт." RelativePanel.AlignVerticalCenterWithPanel="True" RelativePanel.RightOf="Count" Margin="0,0,3,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="Equals_sign" Text="=" RelativePanel.RightOf="Pieces" RelativePanel.AlignVerticalCenterWithPanel="True" Margin="0,0,3,0" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="PriceCount" Text="{x:Bind CountPrice}" RelativePanel.RightOf="Equals_sign" RelativePanel.AlignVerticalCenterWithPanel="True" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="WarrantyField" Text="Гарантия:" RelativePanel.LeftOf="Warranty" RelativePanel.AlignBottomWithPanel="True" Margin="10,15,0,10" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <TextBlock x:Name="Warranty" Text="{x:Bind Warranty}" RelativePanel.LeftOf="ImageExControl" RelativePanel.AlignBottomWithPanel="True" Margin="3,15,5,10" FontSize="16" Style="{StaticResource CaptionTextBlockStyle}"/>
                                        <controls:ImageEx x:Name="ImageExControl"
                                                  Height="auto"
                                                  Width="80"
                                                  Source="{x:Bind Image_URL}"
                                                  Margin="20,0,5,0"
                                                  RelativePanel.AlignRightWithPanel="True"/>
                                    </RelativePanel>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                            <ListView.ItemContainerStyle>
                                <Style TargetType="ListViewItem">
                                    <Setter Property="HorizontalContentAlignment"  Value="Stretch"></Setter>
                                </Style>
                            </ListView.ItemContainerStyle>
                        </ListView>
                        <RelativePanel Grid.Column="1" Margin="0,0,0,0" Padding="10" BorderBrush="Black" BorderThickness="1">
                            <TextBlock x:Name="OrderFrom" Text="Создан" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" Margin="0,0,3,0"/>
                            <TextBlock x:Name="OrderDate" Text="12.12.12 14/14/25 AM" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.RightOf="OrderFrom" RelativePanel.AlignBottomWith="OrderFrom" Margin="5,0,0,0"/>
                            <TextBlock x:Name="StatusField" Text="Статус заказа:" Margin="0,10,0,0" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.Below="OrderDate"/>
                            <TextBlock x:Name="Status" Text="{x:Bind _OrderInfoVM.Status, Mode=OneWay}" Margin="5,0,0,0" FontSize="24" Foreground="{StaticResource SystemAccentColor}" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.RightOf="StatusField" RelativePanel.AlignBottomWith="StatusField"/>
                            <ComboBox x:Name="ChangeStatusCBB" Margin="0,5,0,5" ItemsSource="{x:Bind _OrderInfoVM.OrderStatuses, Mode=OneWay}" DisplayMemberPath="Status" RelativePanel.Below="StatusField" RelativePanel.AlignRightWithPanel="True" Width="164" Header="Изменить статус" PlaceholderText="Выбрать статус"/>
                            <TextBlock x:Name="StatusChanged" Text="Дата изменения статуса:" FontSize="20" RelativePanel.Below="ChangeStatusCBB"/>
                            <TextBlock x:Name="StatusChangedDate" Text="{x:Bind _OrderInfoVM.OrderDate}" FontSize="20" RelativePanel.Below="StatusChanged" RelativePanel.AlignRightWithPanel="True"/>
                            <TextBlock x:Name="IsHidden" Text="Удален пользователем:" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.Below="StatusChangedDate"/>
                            <TextBlock x:Name="IsHiddenValue" Text="{x:Bind _OrderInfoVM.IsHidden}" Style="{StaticResource BodyTextBlockStyle}" FontSize="20" RelativePanel.AlignBottomWith="IsHidden" RelativePanel.RightOf="IsHidden" Margin="3,0,0,0"/>
                            <TextBlock x:Name="QuantityInOrder" Text="Элементов в заказе:" FontSize="20" Margin="0,0,3,0" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.Below="IsHidden"/>
                            <TextBlock x:Name="QuantityInOrderInt" Text="20 шт." FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.AlignVerticalCenterWith="QuantityInOrder" RelativePanel.RightOf="QuantityInOrder"/>
                            <TextBlock x:Name="FinalPrice" Text="ИТОГО: " Margin="0,15,3,0" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.Below="QuantityInOrderInt"/>
                            <TextBlock x:Name="FinalPriceValue" Text="32 222 Р" FontSize="20" Style="{StaticResource BodyTextBlockStyle}" RelativePanel.RightOf="FinalPrice" RelativePanel.AlignBottomWith="FinalPrice"/>
                        </RelativePanel>
                    </Grid>
                </ScrollViewer>
            </StackPanel>
        </Grid>
    </Grid>
</Page>
