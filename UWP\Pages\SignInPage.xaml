﻿<Page
    x:Class="MyStoreCOM.Pages.SignInPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:interactivity="using:Microsoft.Xaml.Interactivity"
    xmlns:behaviors="using:Microsoft.Toolkit.Uwp.UI.Animations.Behaviors"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">

    <Grid>
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind _LoginViewModel.IsLoading, Mode=OneWay}" IsActive="True"/>

        <Image x:Name="backgroundIMG" Source="/Assets/Untitled-3.jpg" Stretch="UniformToFill" MinHeight="700" HorizontalAlignment="Center" VerticalAlignment="Center">
            <interactivity:Interaction.Behaviors>
                <behaviors:Blur x:Name="BlurBehavior"
                        Value="3"
                        Duration="3500"
                        Delay="0"
                        EasingType="Linear"
                        EasingMode="EaseOut"
                        AutomaticallyStart="True"/>
            </interactivity:Interaction.Behaviors>
        </Image>
        <ScrollViewer HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
            <Pivot Height="400" Width="500">
                <Pivot.Title>
                    <TextBlock Text="Авторизация в интернет-магазине" Style="{StaticResource SubheaderTextBlockStyle}" FontSize="28" FontWeight="Medium" Foreground="White"/>
                </Pivot.Title>
                <PivotItem>
                    <PivotItem.Header>
                        <TextBlock Text="Вход" Style="{StaticResource SubtitleTextBlockStyle}" Foreground="white"/>
                    </PivotItem.Header>
                    <StackPanel Orientation="Vertical" Width="476" Height="240" VerticalAlignment="Center" HorizontalAlignment="Right">
                        <TextBlock Text="Для входа введите свой логин и пароль" Margin="0,0,0,10" Foreground="White" FontFamily="Times New Roman" Width="auto" TextAlignment="Center" Style="{StaticResource BodyTextBlockStyle}" FontSize="20"/>
                        <TextBox x:Name="UsernameTextBox" Foreground="Black" Background="White" BorderThickness="1" Margin="2" Width="246" KeyUp="Credentials_KeyUp">
                            <TextBox.Header>
                                <TextBlock Text="Логин" Foreground="White" Style="{StaticResource BodyTextBlockStyle}"/>
                            </TextBox.Header>
                        </TextBox>
                        <PasswordBox x:Name="PasswordTextBox" BorderThickness="1" Margin="2" Width="246" Background="White" KeyUp="Credentials_KeyUp">
                            <PasswordBox.Header>
                                <TextBlock Text="Пароль" Foreground="White" Style="{StaticResource BodyTextBlockStyle}"/>
                            </PasswordBox.Header>
                        </PasswordBox>
                        <Button x:Name="SignInButton" Background="DeepSkyBlue" Foreground="DodgerBlue" Click="SignInButton_Click" Width="154" Height="35" HorizontalAlignment="Center" Margin="0,20,0,30" TabFocusNavigation="Cycle">
                            <Button.Content>
                                <TextBlock Text="Войти" Foreground="Black"/>
                            </Button.Content>
                        </Button>
                        <ProgressBar x:Name="loading_bar" Width="130" IsIndeterminate="True" ShowPaused="False" ShowError="False" Visibility="Collapsed"/>
                    </StackPanel>
                </PivotItem>
                <PivotItem>
                    <PivotItem.Header>
                        <TextBlock Text="Регистрация" Style="{StaticResource SubtitleTextBlockStyle}" Foreground="white"/>
                    </PivotItem.Header>
                    <StackPanel Orientation="Vertical" Width="476" Height="300" VerticalAlignment="Center" HorizontalAlignment="Right">
                        <TextBlock Text="" Margin="0,0,0,10" Foreground="White" FontFamily="Times New Roman" Width="auto" TextAlignment="Center" Style="{StaticResource BodyTextBlockStyle}" FontSize="20"/>
                        <TextBox x:Name="UsernameTextBox2" Foreground="Black" Background="White" BorderThickness="3" BorderBrush="{StaticResource SystemAccentColor}" Margin="2" Width="246" KeyUp="UsernameTextBox2_KeyUp">
                            <TextBox.Header>
                                <TextBlock Text="Логин" Foreground="White" Style="{StaticResource BodyTextBlockStyle}"/>
                            </TextBox.Header>
                        </TextBox>
                        <PasswordBox x:Name="PasswordTextBox2" BorderThickness="3" BorderBrush="{StaticResource SystemAccentColor}" Margin="2" Width="246" Background="White" KeyUp="UsernameTextBox2_KeyUp" PlaceholderText="не менее 6 символов" PasswordRevealMode="Peek">
                            <PasswordBox.Header>
                                <TextBlock Text="Пароль" Foreground="White" Style="{StaticResource BodyTextBlockStyle}"/>
                            </PasswordBox.Header>
                        </PasswordBox>
                        <PasswordBox x:Name="CheckPasswordTextBox2" BorderThickness="3" BorderBrush="{StaticResource SystemAccentColor}" Margin="2" Width="246" Background="White" KeyUp="UsernameTextBox2_KeyUp" PlaceholderText="проверка пароля">
                            <PasswordBox.Header>
                                <TextBlock Text="Повтор пароля" Foreground="White" Style="{StaticResource BodyTextBlockStyle}"/>
                            </PasswordBox.Header>
                        </PasswordBox>
                        <Button x:Name="SignUpBTN" Background="{StaticResource SystemAccentColorLight2}" Foreground="DodgerBlue" Click="SignUpBTN_Click" Width="154" Height="35" HorizontalAlignment="Center" Margin="0,20,0,30" TabFocusNavigation="Cycle">
                            <Button.Content>
                                <TextBlock Text="Зарегистрироваться" Foreground="Black"/>
                            </Button.Content>
                        </Button>
                        <ProgressBar x:Name="loading_bar2" Width="130" IsIndeterminate="True" ShowPaused="False" ShowError="False" Visibility="Collapsed"/>
                    </StackPanel>
                </PivotItem>
            </Pivot>

        </ScrollViewer>
        <controls:Loading x:Name="LoadingControl">
            <controls:Loading.Background>
                <SolidColorBrush Color="Black" Opacity="0.8"/>
            </controls:Loading.Background>
            <StackPanel Orientation="Vertical">
                <ProgressRing IsActive="True" Height="90" Width="90"/>
                <TextBlock Text="Выполняется вход" Foreground="White" HorizontalTextAlignment="Center" FontSize="20"/>
            </StackPanel>
        </controls:Loading>
    </Grid>
</Page>
