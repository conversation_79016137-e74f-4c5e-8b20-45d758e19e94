﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using MyStoreCom.Data;
using Newtonsoft.Json;

namespace MyStoreCom.ViewModels
{
    public class CatalogItemInfoVM
    {
        private static readonly Configuration config = new Configuration();

        public int UserID;

        public async Task<HttpStatusCode> AddToCart(int tovarcod)
        {
            using (HttpClient client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(tovarcod);
                var stringContent = new StringContent(json, Encoding.UTF8, "application/json");
                HttpResponseMessage response = await client.PostAsync(new Uri("https://mystorecomapi.ecsogy.ru/Cart?user_id=" + UserID + ""), stringContent);
                return response.StatusCode; //(HttpStatusCode.Ok);
            }
        }
    }
}