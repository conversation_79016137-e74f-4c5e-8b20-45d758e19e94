﻿using Android.Content;
using Android.Preferences;
using MyStoreCom.Data;
using MyStoreCom.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace MyStoreCom.ViewModels
{
    public class CartItemsVM
    {
        /// <summary>
        /// Gets all the items in user's cart
        /// </summary>
        public int finalPrice;
        readonly Configuration config = new Configuration();
        public List<CartItem> CartItems = new List<CartItem>();
        public int UserID;
        public int CountItems, TotalPrice;

        public async Task<List<CartItem>> LoadItems()
        {
            using (HttpClient client = new HttpClient())
            {
                //Get car of the specified user
                Uri uri = new Uri(config.DefauldAPI_URL + "Cart?user_id="+ UserID + "");
                HttpResponseMessage responseMessage = await client.GetAsync(uri);
                if (responseMessage.IsSuccessStatusCode)
                {
                    var JsonResponse = await client.GetStringAsync(uri);
                    CartItems = JsonConvert.DeserializeObject<List<CartItem>>(JsonResponse);
                }
                var qqq = new Methods();
                foreach (var item in CartItems)
                {
                    item.Image = await qqq.GetImageBitmapFromUrl(config.DefauldAPI_URL + "Content/Images/Catalog/" + item.TovarCod + ".jpg ");
                }

                return CartItems;
            }
        }

        public async Task DeleteItem(int item_position)
        {
            int tovarcodtodelete = CartItems[item_position].TovarCod;
            using (HttpClient client = new HttpClient())
            {
                Uri uri = new Uri("https://mystorecomapi.ecsogy.ru/Cart?user_id=" + UserID + "&tovarkod=" + tovarcodtodelete + "");
                var response = await client.DeleteAsync(uri);
                response.EnsureSuccessStatusCode();
            }
        }

        public async Task<bool> CreateOrder()
        {
            using (HttpClient client = new HttpClient())
            {
                Uri uri = new Uri("https://mystorecomapi.ecsogy.ru/Orders?userid=" + UserID + "");
                HttpResponseMessage responseMessage = await client.PostAsync(uri, null);
                return responseMessage.IsSuccessStatusCode;
            }
        }
    }
}