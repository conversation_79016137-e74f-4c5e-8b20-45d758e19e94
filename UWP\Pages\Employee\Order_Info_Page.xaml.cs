﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.ViewModels.Employee;
using Microsoft.Toolkit.Uwp.Helpers;
using MyStoreCOM.Models.Emloyee;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages.Employee
{
    public sealed partial class Order_Info_Page : Page
    {
        public OrderInfoViewModelEmp _OrderInfoVM = new OrderInfoViewModelEmp();

        public Order_Info_Page()
        {
            this.InitializeComponent();
            _OrderInfoVM.OrderStatuses = StatusesManager.GetOrderStatuses();
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            ActiveOrder order = (ActiveOrder)e.Parameter;
            if (await _OrderInfoVM.LoadOrder(order.OrderNumber))
            {
                Orderid.Text = order.OrderNumber.ToString();
                OrderDate.Text = order.OrderDate;
                StatusChangedDate.Text = _OrderInfoVM.StatusChanged;
                Username.Text = _OrderInfoVM.UsrFullName;
                Userid.Text = _OrderInfoVM.UserID.ToString();
                Userphone.Text = _OrderInfoVM.UsrPhoneNumber;
                IsHiddenValue.Text = _OrderInfoVM.GetVisibilityOfOrder(_OrderInfoVM.IsHidden);
                FinalPriceValue.Text = _OrderInfoVM.TotalPrice.ToString("## ### ₽");
                QuantityInOrderInt.Text = _OrderInfoVM.QuantityInOrderInt.ToString("## ###  шт'.'");
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { ContentGrid.Visibility = Visibility.Visible; });
            }
            else
            {
                RetryBTN.Visibility = Visibility.Visible;
            }
        }
    }
}
