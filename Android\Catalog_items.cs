﻿using Android.App;
using Android.Graphics;
using Android.OS;
using Android.Support.V7.App;
using Android.Views;
using Android.Widget;
using MyStoreCom.ViewModels;
using MyStoreCom.Model;
using System.Collections.Generic;
using Android.Support.V7.Widget;

namespace MyStoreCom
{
    [Activity(Label = "Каталог > Компьютеры", Theme = "@style/AppTheme", MainLauncher = false)]
    public class Catalog_items : AppCompatActivity
    {
        readonly CatalogItemsVM _CatalogItemVM = new CatalogItemsVM(); //Instance of Catalog Items View Model

        RecyclerView mRecyclerView;
        RecyclerView.LayoutManager mLayoutManager;
        CatalogItemsAdapter mAdapter;

        ListView listView;

        protected override async void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            Xamarin.Essentials.Platform.Init(this, savedInstanceState);

            _CatalogItemVM.catalogItemslist = await _CatalogItemVM.GetUsersAsync();

            // Set our view from the "main" layout resource
            SetContentView(Resource.Layout.catalogListView_main);

            // Get our RecyclerView layout:
            mRecyclerView = FindViewById<RecyclerView>(Resource.Id.recyclerView);

            mLayoutManager = new LinearLayoutManager(this);
            mRecyclerView.SetLayoutManager(mLayoutManager);

            mAdapter = new CatalogItemsAdapter(_CatalogItemVM.catalogItemslist);

            mAdapter.ItemClick += OnItemClick;

            mRecyclerView.SetAdapter(mAdapter);
        }

        void OnItemClick(object sender, int position)
        {
            // Display a toast that briefly shows the enumeration of the selected photo:
            int photoNum = position + 1;
            Toast.MakeText(this, "This is photo number " + photoNum, ToastLength.Short).Show();
        }
    }

    public class CatalogViewHolder : RecyclerView.ViewHolder
    {
        public ImageView Image { get; private set; }
        public TextView Title { get; private set; }
        public TextView Price { get; private set; }
        public CatalogViewHolder (View itemView, System.Action<int> listener)
            :base (itemView)
        {
            // Locate and cache view references:
            Image = itemView.FindViewById<ImageView>(Resource.Id.imageView1);
            Title = itemView.FindViewById<TextView>(Resource.Id.textView11);
            Price = itemView.FindViewById<TextView>(Resource.Id.textView22);

            // Detect user clicks on the item view and report which item
            // was clicked (by layout position) to the listener:
            itemView.Click += (sender, e) => listener(base.LayoutPosition);
        }
    }

    public class CatalogItemsAdapter : RecyclerView.Adapter
    {

        // Event handler for item clicks:
        public event System.EventHandler<int> ItemClick;
        public List<CatalogItem> ListItems;

        public CatalogItemsAdapter(List<CatalogItem> listofitems)
        {
            ListItems = listofitems;
        }

        public override RecyclerView.ViewHolder
            OnCreateViewHolder(ViewGroup parent, int viewType)
        {
            // Inflate the CardView for the photo:
            View itemView = LayoutInflater.From(parent.Context).
                        Inflate(Resource.Layout.itemscatalog_main, parent, false);

            // Create a ViewHolder to find and hold these view references, and 
            // register OnClick with the view holder:
            CatalogViewHolder vh = new CatalogViewHolder(itemView, OnClick);
            return vh;
        }

        public override void
            OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
        {
            CatalogViewHolder vh = holder as CatalogViewHolder;

            vh.Image.SetImageBitmap (ListItems[position].BitmapImage);
            vh.Title.Text = ListItems[position].Naimenovenie;
            vh.Price.Text = ListItems[position].PriceSTR;

        }

        public override int ItemCount
        {
            get { return ListItems.Count; }
        }

        void OnClick (int position)
        {
            if (ItemClick != null)
                ItemClick(this, position);
        }
    }




    /*public class CatalogItemsAdapter : BaseAdapter<CatalogItem>
    {
        readonly List<CatalogItem> _itemsInCatalog;
        readonly Activity _activity;
        public CatalogItemsAdapter(Activity currentActivity, List<CatalogItem> catalogitems)
            : base()
        {
            this._activity = currentActivity;
            this._itemsInCatalog = catalogitems;
        }
        public override long GetItemId(int position)
        {
            return position;
        }
        public override CatalogItem this[int position]
        {
            get { return _itemsInCatalog[position]; }
        }
        public override int Count
        {
            get { return _itemsInCatalog.Count; }
        }
        public override View GetView(int position, View convertView, ViewGroup parent)
        {
            var item = _itemsInCatalog[position];

            View view = convertView;

            if (view == null)
                view = _activity.LayoutInflater.Inflate(Resource.Layout.itemscatalog_main, null);
            view.FindViewById<TextView>(Resource.Id.textView11).Text = item.Naimenovenie;
            view.FindViewById<TextView>(Resource.Id.textView22).Text = item.PriceSTR;
            view.FindViewById<ImageView>(Resource.Id.imageView1).SetImageBitmap(item.BitmapImage);

            return view;
        }
    }*/
}