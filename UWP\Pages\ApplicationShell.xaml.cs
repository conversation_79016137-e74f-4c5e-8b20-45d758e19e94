﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using Windows.UI.Xaml.Media.Animation;
using MyStoreCOM.Pages.Employee;
using System.Windows.Input;
using System.ComponentModel;
using Windows.ApplicationModel.Core;
using Windows.UI.ViewManagement;
using Windows.UI;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    public class NavigationViewTemplateSelector : DataTemplateSelector
    {
        public DataTemplate ActiveOrderTemplate { get; set; }
        protected override DataTemplate SelectTemplateCore(object item, DependencyObject container)
        {
            return ActiveOrderTemplate;
        }
    }

    public sealed partial class ApplicationShell : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private int LoggedUserID = (App.Current as App).Current_UserID;

        public ApplicationShell()
        {
            this.InitializeComponent();

        }

        public Frame AppFrame => ContentFrame;

        public Visibility EmpPanelVisibility;

        public Visibility UserPanelVisibility;

        private ScrollBarVisibility _verticalScrollVisibility;
        public ScrollBarVisibility VerticalScrollVisibility
        {
            get { return _verticalScrollVisibility; }
            set
            {
                if (_verticalScrollVisibility == value)
                    return;
                _verticalScrollVisibility = value;
                PropertyChanged(this, new PropertyChangedEventArgs("VerticalScrollVisibility"));
            }
        }

        private void NavigationPanel_Loaded(object sender, RoutedEventArgs e)
        {
            //if (UserPanelVisibility == Visibility.Visible)
           // {
                //NavigationPanel.PaneTitle = "Александр";
          //  }
            //set the initial SelectedItem
            foreach (NavigationViewItemBase item in NavigationPanel.MenuItems)
            {
                if (item is NavigationViewItem && item.Tag.ToString() == "Main")
                {
                    NavigationPanel.SelectedItem = item;
                    break;
                }
            }
        }

        private void NavigationPanel_SelectionChanged(NavigationView sender, NavigationViewSelectionChangedEventArgs args)
        {
            if (args.IsSettingsSelected)
            {
                ContentFrame.Navigate(typeof(SettingsPage));
            }
            else
            {
                NavigationViewItem item = args.SelectedItem as NavigationViewItem;
                NavigationView_Navigate(item);
            }
        }

        private void NavigationView_Navigate(NavigationViewItem item)
        {
            VerticalScrollVisibility = ScrollBarVisibility.Visible;
            switch (item.Tag)
            {
                case "computers":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "Main":
                    ContentFrame.Navigate(typeof(MainPage));
                    break;
                case "monoblocks":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "laptops":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "tablets":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "smartphones":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "other":
                    ContentFrame.Navigate(typeof(CatalogPage), item);
                    break;
                case "Orders":
                    ContentFrame.Navigate(typeof(OrdersPage));
                    break;
                case "Basket":
                    ContentFrame.Navigate(typeof(CartPage));
                    break;
                case "ActiveOrdersEmp":
                    ContentFrame.Navigate(typeof(Orders_list_Page));
                    VerticalScrollVisibility = ScrollBarVisibility.Disabled;
                    break;
            }
        }

        private void NavigationPanel_BackRequested(NavigationView sender, NavigationViewBackRequestedEventArgs args)
        {
            On_BackRequested();
        }

        /*private void BackInvoked(KeyboardAccelerator sender,
                         KeyboardAcceleratorInvokedEventArgs args)
        {
            On_BackRequested();
            args.Handled = true;
        }*/

        private bool On_BackRequested()
        {
            if (!ContentFrame.CanGoBack)
                return false;

            // Don't go back if the nav pane is overlayed.
            if (NavigationPanel.IsPaneOpen &&
                (NavigationPanel.DisplayMode == NavigationViewDisplayMode.Compact ||
                 NavigationPanel.DisplayMode == NavigationViewDisplayMode.Minimal))
                return false;

            ContentFrame.GoBack();
            return true;
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            EmpPanelVisibility = Visibility.Collapsed;
            UserPanelVisibility = Visibility.Collapsed;
            if ((string)e.Parameter == "logged") // logged
            {
                UserPanelVisibility = Visibility.Visible;
                if ((App.Current as App).IsEmployee) // Check ifEmployee
                {
                    EmpPanelVisibility = Visibility.Visible;
                }
            }
            CoreApplication.GetCurrentView().TitleBar.ExtendViewIntoTitleBar = false;
            ApplicationViewTitleBar titleBar = ApplicationView.GetForCurrentView().TitleBar;
            titleBar.BackgroundColor = (Color)Resources["SystemAccentColor"];
            titleBar.InactiveBackgroundColor = (Color)Resources["SystemAccentColor"];
            titleBar.ButtonBackgroundColor = (Color)Resources["SystemAccentColor"];
            titleBar.ButtonInactiveBackgroundColor = (Color)Resources["SystemAccentColor"];
            titleBar.ButtonForegroundColor = (Color)this.Resources["SystemBaseHighColor"];
            NavigationPanel.IsBackEnabled = ContentFrame.CanGoBack;

            /* var oo = e.Parameter;
            //UserPanelVisibility = Visibility.Visible;
            NavigationPanel.PaneTitle = "Александр";
            base.OnNavigatedTo(e);
            */

            if (ContentFrame.SourcePageType == typeof(SettingsPage))
            {
                // SettingsItem is not part of NavigationPanel.MenuItems, and doesn't have a Tag.
                NavigationPanel.SelectedItem = (NavigationViewItem)NavigationPanel.SettingsItem;
                NavigationPanel.Header = "Settings";
            }
            else if (ContentFrame.SourcePageType != null)
            {
                /* NavigationPanel.SelectedItem = NavigationPanel.MenuItems
                   .OfType<NavigationViewItem>()
                     .First(n => n.NavigationPanel.MenuItems.Equals(item.Tag));

                 NavigationPanel.Header =
                     ((NavigationViewItem)NavigationPanel.SelectedItem)?.Content?.ToString(); */
            }
            base.OnNavigatedTo(e);
        }

        private void ContentFrame_Navigated(object sender, NavigationEventArgs e)
        {
            foreach (NavigationViewItemBase item in NavigationPanel.MenuItems)
            {

            }
        }

        /// <summary>
        /// For setting header of the NavigationView
        /// </summary>
        /// <param name="header"></param>
        public void SetHeader(string header)
        {
            NavigationPanel.Header = header;
        }

        private void SignInBtn_Tapped(object sender, TappedRoutedEventArgs e)
        {
            if (LoggedUserID == 0)
            {
                this.Frame.Navigate(typeof(SignInPage), null, new SlideNavigationTransitionInfo() { Effect = SlideNavigationTransitionEffect.FromLeft });
            }
        }
    }
}
