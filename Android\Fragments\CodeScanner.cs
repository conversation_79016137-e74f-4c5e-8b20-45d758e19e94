﻿using System;
using Android.Gms.Vision;
using Android.Gms.Vision.Barcodes;
using Android.Graphics;
using Android.Support.V4.App;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using static Android.Gms.Vision.Detector;
using Android;
using Android.Content.PM;
using MyStoreCom.ViewModels;


namespace MyStoreCom.Fragments
{
    public class CodeScanner : Android.Support.V4.App.Fragment, ISurfaceHolderCallback, IProcessor
    {
        View myFragmentView;

        private CodeScannerVM _codescannerVM = new CodeScannerVM();

        SurfaceView surfaceView;

        TextView txtResult;

        BarcodeDetector barcodeDetector;

        CameraSource cameraSource;

        RelativeLayout ProgressRingLayout;

        const int RequestCameraPermisionID = 1001;


        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            myFragmentView = inflater.Inflate(Resource.Layout.code_scanner, container, false);
            //Button button = myFragmentView.FindViewById<Button>(Resource.Id.buttonScanDefaultView);
            surfaceView = myFragmentView.FindViewById<SurfaceView>(Resource.Id.cameraView);
            txtResult = myFragmentView.FindViewById<TextView>(Resource.Id.txtResult);
            barcodeDetector = new BarcodeDetector.Builder(this.Context)
                //.SetBarcodeFormats(BarcodeFormat.)
                .Build();
            cameraSource = new CameraSource
                .Builder(this.Context, barcodeDetector)
                .SetAutoFocusEnabled(true)
                .SetRequestedPreviewSize(640, 480)
                .Build();
            surfaceView.Holder.AddCallback(this);
            barcodeDetector.SetProcessor(this);

            ProgressRingLayout = myFragmentView.FindViewById<RelativeLayout>(Resource.Id.codescanner_progresslayout);

            return myFragmentView;
        }


        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Permission[] grantResults)
        {
            switch (requestCode)
            {
                case RequestCameraPermisionID:
                    {
                        if (grantResults[0] == Permission.Granted)
                        {
                            if (ActivityCompat.CheckSelfPermission(this.Activity, Manifest.Permission.Camera) != Android.Content.PM.Permission.Granted)
                            {
                                //Request Permision  
                                ActivityCompat.RequestPermissions(this.Activity, new string[]
                                {
                    Manifest.Permission.Camera
                                }, RequestCameraPermisionID);
                                return;
                            }
                            try
                            {
                                cameraSource.Start(surfaceView.Holder);
                            }
                            catch (InvalidOperationException)
                            {
                            }
                        }
                    }
                    break;
            }
        }

        public void SurfaceChanged(ISurfaceHolder holder, [GeneratedEnum] Format format, int width, int height)
        {
        }
        public void SurfaceCreated(ISurfaceHolder holder)
        {
            if (ActivityCompat.CheckSelfPermission(this.Context, Manifest.Permission.Camera) != Android.Content.PM.Permission.Granted)
            {
                //Request Permision  
                ActivityCompat.RequestPermissions(this.Activity, new string[]
                {
                    Manifest.Permission.Camera
                }, RequestCameraPermisionID);
                return;
            }
            try
            {
                cameraSource.Start(surfaceView.Holder);
            }
            catch (InvalidOperationException)
            {
            }

        }
        public void SurfaceDestroyed(ISurfaceHolder holder)
        {
            cameraSource.Stop();
        }
        public void ReceiveDetections(Detections detections)
        {
            SparseArray qrcodes = detections.DetectedItems;
            if (qrcodes.Size() != 0)
            {
                txtResult.Post(() => {
                    cameraSource.Stop();
                    ProgressRingLayout.Visibility = ViewStates.Visible;
                    txtResult.Text = ((Barcode)qrcodes.ValueAt(0)).RawValue;
                    StartSearching(((Barcode)qrcodes.ValueAt(0)).RawValue);
                });
            }
        }
        public void Release()
        {

        }

        private async void StartSearching(string searchtext)
        {
            var FoundItem = await _codescannerVM.SearchItemByBarcode(searchtext);
            if (FoundItem == null)
            {
                FragmentManager fragmentManager = Activity.SupportFragmentManager;
                FragmentTransaction trans = fragmentManager.BeginTransaction();
                trans.Replace(Resource.Id.mainFrame, new Search(searchtext), "Search. Not found by barcode");
                Activity.Title = "Результаты поиска";
                trans.Commit();
            }
            else
            {
                FragmentManager fragmentManager = Activity.SupportFragmentManager;
                FragmentTransaction trans = fragmentManager.BeginTransaction();
                trans.Replace(Resource.Id.mainFrame, new CatalogItemInfo(FoundItem), "Catalog Item From search");
                Activity.Title = "Результаты поиска";
                trans.Commit();
            }

            //trans.Replace(Resource.Id.mainFrame, new CodeScanner(), "Code Scanner");
            //Title = "Поиск по штрих-коду";
        }
    }
}