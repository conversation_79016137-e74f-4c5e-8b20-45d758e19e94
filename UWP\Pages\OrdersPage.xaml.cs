﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.ViewModels;
using MyStoreCOM.Models;
using Microsoft.Toolkit.Uwp.Helpers;
using MyStoreCOM.Models.Emloyee;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class OrdersPage : Page
    {
        public OrdersPageViewModel Orders { get; } = new OrdersPageViewModel();
        public OrdersPage()
        {
            this.InitializeComponent();
            Orders.OrderStatuses = StatusesManager.GetOrderStatuses();
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Orders.IsLoading = true; }); // change the visibility of PrograssBar (Ring)
            if (await Orders.LoadOrders())
            {
                if (Orders.Ordersmain.Count == 0)
                {
                    EmptyOrdersPageLayout.Visibility = Visibility.Visible;
                }
                else MyMainList.Visibility = Visibility.Visible;
            }
            else
            {

            }
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Orders.IsLoading = false; }); // change the visibility of PrograssBar (Ring)
        }

        private async void RemoveOrder_Click(object sender, RoutedEventArgs e)
        {
            var btn = (Button)sender;
            int OrderNum = Convert.ToInt32(btn.Tag);
            await Orders.DeleteOrder(OrderNum);
            if (Orders.Ordersmain.Count == 0)
            {
                MyMainList.Visibility = Visibility.Collapsed;
                EmptyOrdersPageLayout.Visibility = Visibility.Visible;
            }
        }

        private void SubItemsList_layout_ItemClick(object sender, ItemClickEventArgs e)
        {
            var clickeditem = e.ClickedItem;
            Frame.Navigate(typeof(CatalogItem), clickeditem);
        }
    }
}
