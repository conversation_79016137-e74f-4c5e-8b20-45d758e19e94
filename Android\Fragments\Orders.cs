﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Support.V4.Widget;
using Android.Support.V7.Widget;
using Android.Util;
using Android.Views;
using Android.Widget;
using FFImageLoading;
using MyStoreCom.Model;
using MyStoreCom.ViewModels;
using MyStoreCom.Data;
using Android.Preferences;

namespace MyStoreCom.Fragments
{
    public class Orders : Android.Support.V4.App.Fragment
    {
        private static readonly Configuration config = new Configuration();
        View fragmentView = null;
        private static OrdersVM _ordersVM = new OrdersVM();
        private static OrderParentAdapter ParentAdapter;
        RecyclerView.LayoutManager layoutManager;
        SwipeRefreshLayout swiper;

        public override async void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            ISharedPreferences prefs = PreferenceManager.GetDefaultSharedPreferences(Activity);
            _ordersVM.UserID = prefs.GetInt("user_id", 0);
            var ListofOrders = await _ordersVM.LoadOrders();
            RecyclerView mRecyclerView = fragmentView.FindViewById<RecyclerView>(Resource.Id.orders_rv_parent);
            ParentAdapter = new OrderParentAdapter(ListofOrders);
            mRecyclerView.SetAdapter(ParentAdapter);
            layoutManager = new LinearLayoutManager(this.Activity);
            mRecyclerView.SetLayoutManager(layoutManager);
            swiper = fragmentView.FindViewById<SwipeRefreshLayout>(Resource.Id.orders_swiperefresh);
            swiper.Refresh += Swiper_Refresh;

            //ParentAdapter.ParentItemClick += OnItemClick; // needed to create an action
        }

        private async void Swiper_Refresh(object sender, EventArgs e)
        {
            var loadingItems = _ordersVM.LoadOrders(); // start loading items
            ParentAdapter.Orders.Clear();
            ParentAdapter.Orders.AddRange(await loadingItems); // wait until all items are arrived
            ParentAdapter.NotifyDataSetChanged();
            swiper.Refreshing = false;
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            fragmentView = inflater.Inflate(Resource.Layout.order_main, container, false);

            return fragmentView;
        }

        public class OrderChildViewHolder : RecyclerView.ViewHolder
        {
            public TextView Title { get; private set; }
            public TextView Price { get; private set; }
            public TextView ItemQuantity { get; private set; }
            public ImageView Image { get; private set; }
            public OrderChildViewHolder(View itemView, System.Action<int> listener) : base(itemView)
            {
                Title = itemView.FindViewById<TextView>(Resource.Id.orders_child_title);
                Price = itemView.FindViewById<TextView>(Resource.Id.orders_child_itemprice);
                ItemQuantity = itemView.FindViewById<TextView>(Resource.Id.orders_child_itemquantity);
                Image = itemView.FindViewById<ImageView>(Resource.Id.orders_child_image);
            }
        }
        public class OrderParentViewHolder : RecyclerView.ViewHolder
        {
            public RecyclerView ChildRecycle { get; private set; }
            public TextView OrderID { get; private set; }
            public TextView OrderDate { get; private set; }
            public ImageButton DeleteOrder { get; private set; }

            public OrderParentViewHolder(View itemView2, System.Action<int> listener) : base(itemView2)
            {
                ChildRecycle = itemView2.FindViewById<RecyclerView>(Resource.Id.orders_recyclerview_child);
                OrderID = itemView2.FindViewById<TextView>(Resource.Id.orders_parent_textview);
                OrderDate = itemView2.FindViewById<TextView>(Resource.Id.ord_parent_orderdate);
                DeleteOrder = itemView2.FindViewById<ImageButton>(Resource.Id.orders_delete);
            }
        }

        public class OrderParentAdapter : RecyclerView.Adapter
        {
            public event System.EventHandler<int> ParentItemClick;
            public List<Order> Orders;
            public OrderParentViewHolder vhParent;
            public OrderParentAdapter(List<Order> listofitems)
            {
                Orders = listofitems;
            }
            public override RecyclerView.ViewHolder
                OnCreateViewHolder(ViewGroup parent, int viewType)
            {
                View parentView = LayoutInflater.From(parent.Context).Inflate(Resource.Layout.orders_parent, parent, false);
                vhParent = new OrderParentViewHolder(parentView, OnClick);
                return vhParent;
            }

            public override void OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
            {
                OrderParentViewHolder parentVH = holder as OrderParentViewHolder;
                parentVH.OrderID.Text = Orders[position].OrderNumber.ToString();
                parentVH.OrderDate.Text = Orders[position].OrderDate.ToString();
                if (!parentVH.DeleteOrder.HasOnClickListeners)
                {
                    parentVH.DeleteOrder.Click += delegate
                    {
                        DeleteOrder_Click(Convert.ToInt32(parentVH.OrderID.Text));
                    };
                }

                // nested recyclerView next
                var ChildAdapter = new OrderChildAdapter(Orders[position].SubItemsList);
                parentVH.ChildRecycle.SetAdapter(ChildAdapter); // setting sub items adapter to parent recyclerview
                var childlayoutManager = new LinearLayoutManager(parentVH.ChildRecycle.Context, LinearLayoutManager.Horizontal, false); //create layout for sub items in the parent recyclerview
                parentVH.ChildRecycle.SetLayoutManager(childlayoutManager);
            }

            private async void DeleteOrder_Click(int orderid)
            {
                int indx = 0;
                var deleting = _ordersVM.DeleteOrder(orderid);
                foreach (var order in ParentAdapter.Orders)
                {
                    if (order.OrderNumber == orderid)
                    {
                        var successful = await deleting;
                        if (successful)
                        {
                            ParentAdapter.Orders.RemoveAt(indx);
                            ParentAdapter.NotifyDataSetChanged();
                        }
                        break;
                    }
                    indx++;
                }
            }

            public override int ItemCount
            {
                get { return Orders.Count; }
            }

            public void OnClick(int position)
            {
                ParentItemClick?.Invoke(this, position);
            }
        }

        public class OrderChildAdapter : RecyclerView.Adapter
        {
            public event System.EventHandler<int> ChildItemClick;
            public List<OrderSub> SubItems;

            public OrderChildAdapter(List<OrderSub> listofitems)
            {
                SubItems = listofitems;
            }

            public override RecyclerView.ViewHolder
                OnCreateViewHolder(ViewGroup child, int viewType)
            {
                View childView = LayoutInflater.From(child.Context).Inflate(Resource.Layout.orders_child, child, false);
                OrderChildViewHolder vhChild = new OrderChildViewHolder(childView, OnClick);
                return vhChild;
            }

            public override void OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
            {
                OrderChildViewHolder childVH = holder as OrderChildViewHolder;
                childVH.Title.Text = SubItems[position].Title;
                ImageService.Instance.LoadUrl(config.DefauldAPI_URL + "Content/Images/Catalog/" + SubItems[position].TovarKod + ".jpg").Into(childVH.Image);
                childVH.Price.Text = SubItems[position].PriceS;
                childVH.ItemQuantity.Text = SubItems[position].Count.ToString("### шт.");
            }

            public override int ItemCount
            {
                get { return SubItems.Count; }
            }

            public void OnClick(int position)
            {
                ChildItemClick?.Invoke(this, position);
            }
        }
    }
}