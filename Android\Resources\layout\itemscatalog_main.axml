<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="90dp">
    <ImageView
        android:layout_width="90dp"
        android:layout_height="100dp"
        android:id="@+id/imageView1"
        android:padding="10dp"
        android:paddingLeft="5dp"
        android:paddingRight="10dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"/>
    <TextView
        android:layout_toRightOf="@id/imageView1"
        android:text="Large"
        android:textColor="@android:color/black"
        android:textSize="16dp"
        android:fontFamily="sans-serif"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="2dp"
        android:id="@+id/textView11" />

    <TextView
        android:layout_toRightOf="@id/imageView1"
        android:layout_below="@id/textView11"
        android:text="Small Text"
        android:textSize="18dp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="2dp"
        android:id="@+id/textView22" />
    <ImageButton
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_width="50sp"
        android:layout_height="30sp"
        android:background="@android:color/transparent"
        android:id="@+id/add_to_cartBTN"
        android:src="@drawable/baseline_add_shopping_cart_24" />

</RelativeLayout>
