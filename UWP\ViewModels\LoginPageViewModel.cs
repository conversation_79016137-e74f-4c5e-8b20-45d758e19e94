﻿using Microsoft.Toolkit.Uwp.Helpers;
using MyStoreCOM.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.UI.Popups;
using Windows.UI.Xaml.Controls;
using Windows.Web.Http;
using Windows.Web.Http.Headers;

namespace MyStoreCOM.ViewModels
{
    public class LoginPageViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private bool _isloading;
        public bool IsLoading
        {
            get { return _isloading; }
            set
            {
                if (_isloading == value)
                    return;

                _isloading = value;
                PropertyChanged(this, new PropertyChangedEventArgs("IsLoading"));
            }
        }

        private static readonly HttpClient client = new HttpClient();

        private readonly string _Default_URL = (App.Current as App).DefauldAPI_URL;

        public async Task<bool> SignIn(string login, string password)
        {
            //await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = true; }); // change the visibility of PrograssBar (Ring)
            if (login.Length == 0 || password.Length < 6)
            {
                await Task.Delay(500);
                ContentDialog IncorrectUSRnameORpswrd = new ContentDialog
                {
                    Title = "Введите логин и пароль",
                    Content = "Проверьте правильность ввода логина и пароля.\nПароль должен состоять не менее чем из 5 символов. Логин может быть любой длины",
                    CloseButtonText = "Закрыть"
                };
                await IncorrectUSRnameORpswrd.ShowAsync();
                //await Task.Delay(100);
                return false;
            }
            else
            {
                Uri uri = new Uri("" + _Default_URL + "user/login");
                client.DefaultRequestHeaders.Authorization = new HttpCredentialsHeaderValue("Basic", EncodeCredentials(login, password));
                try
                {
                    HttpResponseMessage responseMessage = await client.GetAsync(uri);
                    if (responseMessage.IsSuccessStatusCode)
                    {
                        var jsonResponse = await client.GetStringAsync(uri);
                        var loginuser = JsonConvert.DeserializeObject<LoginUser>(jsonResponse);
                        (App.Current as App).Current_UserID = loginuser.UserID;
                        if (loginuser.UserType == "user")
                        {
                            (App.Current as App).IsEmployee = false;
                        }
                        else
                        {
                            (App.Current as App).IsEmployee = true;
                        }
                        await Task.Delay(2000);
                        return true;
                    }
                    else
                    {
                        if (responseMessage.StatusCode == HttpStatusCode.NotFound)
                        {
                            var WronCredentials = new MessageDialog("Введенная комбинация имени пользователя и пароля не найдена. \nПовторите попытку еще раз");
                            await WronCredentials.ShowAsync();
                        }
                        else
                        {
                            var ErrorOccured = new MessageDialog("Произошла ошибка. Повторите позже");
                            await ErrorOccured.ShowAsync();
                        }
                        return false;
                    }
                }
                catch
                {
                    var message = new MessageDialog("Проверьте подключение к интернету");
                    await message.ShowAsync();
                    return false;
                }
            }
        }

        public async Task<bool> SignUp(string login, string password1, string password2)
        {
            if (login.Length == 0 || password1.Length <= 5)
            {
                ContentDialog IncorrectUSRnameORpswrd = new ContentDialog
                {
                    Title = "Введите логин и пароль",
                    Content = "Проверьте правильность ввода логина и пароля.\nПароль не может быть меньше 6 символов",
                    CloseButtonText = "Закрыть"
                };
                await IncorrectUSRnameORpswrd.ShowAsync();
                return false;
            }
            else
            {
                if (password1 != password2) // Check if passwords are equal
                {
                    ContentDialog PasswordsNotEqual = new ContentDialog
                    {
                        Title = "Введенные пароли не совпадают",
                        Content = "Введенные пароли должны совпадать. Проверьте правильность ввода паролей",
                        CloseButtonText = "Закрыть"
                    };
                    await PasswordsNotEqual.ShowAsync();
                    return false;
                }
                else // Passwords are equal
                {
                    int NewUserId = 0;
                    client.DefaultRequestHeaders.Authorization = new HttpCredentialsHeaderValue("Basic", EncodeCredentials(login, password1));
                    Uri uri = new Uri("" + _Default_URL + "user/register");
                    try
                    {
                        //IHttpContent ContentToPost = new HttpStringContent(JsonConvert.SerializeObject(EncodeCredentials(login, password1)));
                        var PostResponse = await client.PostAsync(uri, null);
                        if (PostResponse.StatusCode == HttpStatusCode.NotAcceptable) // login already exists
                        {
                            ContentDialog LoginExists = new ContentDialog
                            {
                                Title = "Логин занят",
                                Content = "Пользователь с таким логином уже существует. \nВведите другой логин и попробуйте ещё раз",
                                CloseButtonText = "Закрыть"
                            };
                            await LoginExists.ShowAsync();
                            return false;
                        }
                        else
                        {
                            NewUserId = Convert.ToInt32(PostResponse.Content.ToString());
                            (App.Current as App).Current_UserID = NewUserId;
                            (App.Current as App).IsEmployee = false;
                            return true;
                        }
                    }
                    catch
                    {
                        var message = new MessageDialog("Проверьте подключение к интернету");
                        await message.ShowAsync();
                        return false;
                    }
                }
            }
        }

        public static string EncodeCredentials(string login, string password)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes("" + login + ":" + password + "");
            return System.Convert.ToBase64String(plainTextBytes);
        }
    }
}
