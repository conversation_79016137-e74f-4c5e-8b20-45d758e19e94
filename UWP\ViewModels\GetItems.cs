﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models;
using System.Collections.ObjectModel;
using System.Data.SqlClient;

namespace MyStoreCOM.ViewModels
{
    public class GetItems
    {
        /// <summary>
        /// Query for receiving smartphones
        /// </summary>
        public string QSmartphones { get; } = "SELECT [kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel]FROM[MyStoreCOM].[dbo].[Smartphones]";
        /// <summary>
        /// Query for receiving tablets
        /// </summary>
        public string QTablets { get; } = "SELECT [kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel]FROM[MyStoreCOM].[dbo].[Tablets]";

        public ObservableCollection<Smartphone> Smartphones { get; private set; } = new ObservableCollection<Smartphone>();

        public ObservableCollection<Tablet> Tablets { get; private set; } = new ObservableCollection<Tablet>();

        public void LoadItems(string query, bool smarts)
        {
            if (smarts)
            {
                using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
                {
                    conn.Open();
                    if (conn.State == System.Data.ConnectionState.Open)
                    {
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = query;
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                int i = 1;
                                while (reader.Read())
                                {
                                    var smart = new Smartphone
                                    {
                                        Count = i,
                                        TovarCode = reader.GetInt32(0),
                                        Naimenovanie = reader.GetString(1),
                                        Price = reader.GetInt32(2),
                                        InStock = reader.GetInt32(3),
                                        Warranty = reader.GetInt32(4),
                                        Manufacturer = reader.GetString(5)
                                    };
                                    i++;
                                    Smartphones.Add(smart);
                                }
                            }
                        }
                    }
                    conn.Close();
                }
            }
            else
            {
                using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
                {
                    conn.Open();
                    if (conn.State == System.Data.ConnectionState.Open)
                    {
                        using (SqlCommand cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = query;
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                int i = 1;
                                while (reader.Read())
                                {
                                    var tablet = new Tablet
                                    {
                                        Count = i,
                                        TovarCode = reader.GetInt32(0),
                                        Naimenovanie = reader.GetString(1),
                                        Price = reader.GetInt32(2),
                                        InStock = reader.GetInt32(3),
                                        Warranty = reader.GetInt32(4),
                                        Manufacturer = reader.GetString(5)
                                    };
                                    i++;
                                    Tablets.Add(tablet);
                                }
                            }
                        }
                    }
                    conn.Close();
                }
            }
        }
    }
}
