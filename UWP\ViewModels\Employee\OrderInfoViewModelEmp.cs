﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.Web.Http;
using MyStoreCOM.Models.Emloyee;
using Newtonsoft.Json;
using Microsoft.Toolkit.Uwp.Helpers;
using Windows.UI.Popups;

namespace MyStoreCOM.ViewModels.Employee
{
    public class OrderInfoViewModelEmp : BindableBase
    {
        public ObservableCollection<OrderItems> _ItemsInOrder { get; private set; } = new ObservableCollection<OrderItems>();
        public ObservableCollection<OrderInfoItemsEmp> orderInfoItemsEmps { get; set; } = new ObservableCollection<OrderInfoItemsEmp>();

        private protected readonly string _DefaultAPI_URL = (App.Current as App).DefauldAPI_URL;

        static readonly HttpClient client = new HttpClient();

        private bool _isUpdating;
        public bool IsUpdating
        {
            get => _isUpdating;
            set => Set(ref _isUpdating, value);
        }

        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set => Set(ref _isLoading, value);
        }
        public List<OrderStatus> OrderStatuses;

        public int StatusID;

        private string _Status;
        public string Status
        {
            get => _Status;
            set => Set(ref _Status, value);
        }

        public bool IsHidden;
        public string OrderDate;
        public int UserID;
        public string StatusChanged;
        public string UsrPhoneNumber;
        public string UsrFullName;
        public int QuantityInOrderInt;
        public int TotalPrice;
        //public string TotalPriceSTR;


        public async Task<bool> LoadOrder(int orderid)
        {
            HttpResponseMessage responseMessage = null;
            Uri uri = null;
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = true; }); // change the visibility of PrograssBar (Ring)
            try
            {
                uri = new Uri("" + _DefaultAPI_URL + "employee/order/" + orderid + "");
                responseMessage = await client.GetAsync(uri);
            }
            catch
            {
                var message = new MessageDialog("Проверьте подключение к интернету");
                await message.ShowAsync();
                return false;
            }
            try
            {
                responseMessage.EnsureSuccessStatusCode();
                var jsonResponse = await client.GetStringAsync(uri);
                ObservableCollection<OrderInfoItemsEmp> _order = JsonConvert.DeserializeObject<ObservableCollection<OrderInfoItemsEmp>>(jsonResponse);
                _ItemsInOrder.Clear();
                { // Bind data to the UI
                    StatusID = _order[0].StatusID;
                    switch (StatusID) // find the string value of statusid
                    {
                        case 1:
                            Status = OrderStatuses[0].Status;
                            break;
                        case 2:
                            Status = OrderStatuses[1].Status;
                            break;
                    }
                    IsHidden = _order[0].IsHidden;
                    OrderDate = _order[0].OrderDate;
                    UserID = _order[0].UserID;
                    StatusChanged = _order[0].StatusChanged;
                    UsrPhoneNumber = _order[0].UsrPhoneNumber;
                    UsrFullName = _order[0].UsrFullName;
                }
                QuantityInOrderInt = _order[0].ItemsInOrder.Count; // Get the amount of items in the order
                TotalPrice = 0;
                foreach (var item2 in _order[0].ItemsInOrder) // separate items from nested collection
                {
                    _ItemsInOrder.Add(item2);
                    TotalPrice += item2.Count * item2.Price;
                }
                return true;
            }
            catch
            {
                var message = new MessageDialog("Упсс... Возникла ошибка, повторите позже");
                await message.ShowAsync();
                return false;
            }
            finally
            {
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { IsLoading = false; }); // change the visibility of PrograssBar
            }
        }
        public string GetVisibilityOfOrder(bool _ishidden)
        {
            if (_ishidden) { return "Да"; } else return "Нет";
        }
    }
}
