﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Windows.UI.Xaml.Input;

namespace MyStoreCOM.Models
{
    public class Computer
    {
        //public int Count { get; set; }
        public int Kod_tovara { get; set; }
       // public string Title { get; set; }
        public string Naimenovenie { get; set; }
        public int Price { get; set; }
        public string PriceSTR { get { return Price.ToString("## ### ₽"); } set { } }
        public int Nalichie { get; set; }
        public string InStockS { get { return Nalichie.ToString("### шт'.'"); } set { Nalichie = Convert.ToInt32(value); } }
        public int Warranty { get; set; }
        public string WarrantyS { get { return Warranty.ToString("### мес'.'"); } set { } }
        public string Proizvoditel { get; set; }
       // public int Image_count { get; set; }
        public string Image_URL { get { return (App.Current as App).DefauldAPI_URL + "Content/Images/Catalog/" + Kod_tovara + ".jpg"; }}
    }
}
