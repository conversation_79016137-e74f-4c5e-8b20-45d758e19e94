﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Data.SqlClient;
using System.Runtime.InteropServices.WindowsRuntime;
using Windows.Foundation;
using Windows.Foundation.Collections;
using Windows.UI.Xaml;
using Windows.UI.Xaml.Controls;
using Windows.UI.Xaml.Controls.Primitives;
using Windows.UI.Xaml.Data;
using Windows.UI.Xaml.Input;
using Windows.UI.Xaml.Media;
using Windows.UI.Xaml.Navigation;
using MyStoreCOM.Models;
using MyStoreCOM.ViewModels;
using Microsoft.Toolkit.Uwp.Helpers;
using MyStoreCOM.Pages;
using System.Data;
using Windows.UI.Xaml.Media.Imaging;

// The Blank Page item template is documented at https://go.microsoft.com/fwlink/?LinkId=234238

namespace MyStoreCOM.Pages
{
    /// <summary>
    /// An empty page that can be used on its own or navigated to within a Frame.
    /// </summary>
    public sealed partial class CatalogPage : Page
    {
        /// <summary>
        /// I use this object to bind the UI to the project's data
        /// </summary>
        public ComputersPageViewModel Computer { get; } = new ComputersPageViewModel();

        public Uri PP = new Uri("https://mystorecomapi.ecsogy.ru/CatalogItems/1");

        private string CatalogCategory;

        public CatalogPage()
        {
            this.InitializeComponent();
        }

        protected override async void OnNavigatedTo(NavigationEventArgs e)
        {
            await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Computer.IsLoading = true; });
            var item = (NavigationViewItem)e.Parameter;
            CatalogCategory = item.Tag.ToString();
            Computer.HeaderOfPage = item.Content.ToString();
            //await Computer.LoadComputers();
            if (await Computer.LoadCatalogItems(CatalogCategory))
            {
                await DispatcherHelper.ExecuteOnUIThreadAsync(() => { Computer.IsLoading = false; });
            }
            else
            {
                RetryBTN.Visibility = Visibility.Visible;
            }

        }
        /// <summary>
        /// Add to the cart a desired item (TovarCod is the value for 'Tag'
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private async void Add_to_cart_Click(object sender, RoutedEventArgs e)
        {
            if ((App.Current as App).Current_UserID == 0)
            {
                ContentDialog NotSignedIn = new ContentDialog
                {
                    Title = "Выполните вход",
                    Content = "Для добавления товаров в корзину и совершения покупок необходимо\nзарегистрироваться, либо выполнить вход под своей учетной записью",
                    CloseButtonText = "Закрыть"
                };
                await NotSignedIn.ShowAsync();
            }
            else
            {
                var btn = (Button)sender;
                int tovarcod = Convert.ToInt32(btn.Tag); //get the tovar cod of the chosen item in catalog
                await Computer.Add_To_CartAsync(tovarcod);
            }
        }

        private void Grid_VIEW_ItemClick(object sender, ItemClickEventArgs e)
        {
            var clickeditem = e.ClickedItem;
            var animation = Grid_VIEW.PrepareConnectedAnimation("ca1", e.ClickedItem, "Title");
            Frame.Navigate(typeof(CatalogItem), clickeditem);
        }

        private async void RetryBTN_Click(object sender, RoutedEventArgs e)
        {
            RetryBTN.Visibility = Visibility.Collapsed;
            if (await Computer.LoadCatalogItems(CatalogCategory))
            {
                //await DispatcherHelper.ExecuteOnUIThreadAsync(() => { ContentGrid.Visibility = Visibility.Visible; });
            }
            else
            {
                RetryBTN.Visibility = Visibility.Visible;
            }
        }
    }
}
