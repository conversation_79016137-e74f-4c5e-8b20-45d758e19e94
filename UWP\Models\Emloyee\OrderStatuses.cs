﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MyStoreCOM.Models.Emloyee
{
    public class OrderStatus
    {
        public int StatusID { get; set; }
        public string Status { get; set; }
    }

    public class StatusesManager
    {
        public static List<OrderStatus> GetOrderStatuses()
        {
            var orderStatuses = new List<OrderStatus>
            {
                new OrderStatus { StatusID = 1, Status = "Создан" },
                new OrderStatus { StatusID = 2, Status = "В обработке" }
            };
            return orderStatuses;
        }
    }
}
