﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;

namespace MyStoreCom.Fragments
{
    public class Main_page : Android.Support.V4.App.Fragment
    {
        View fragmentview;

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // Create your fragment here
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            // Use this to return your custom view for this Fragment
            // return inflater.Inflate(Resource.Layout.YourFragment, container, false);
            fragmentview = inflater.Inflate(Resource.Layout.main_page, container, false);

            return base.OnCreateView(inflater, container, savedInstanceState);
        }
    }
}