﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MyStoreCOM.Models;
using Newtonsoft.Json;
using Windows.Web.Http;

namespace MyStoreCOM.ViewModels
{
    public class CatalogItemVM
    {
        public string ImageURL;
        public string Title;

        public readonly string _DefaultAPI_URL = (App.Current as App).DefauldAPI_URL;

        private static readonly HttpClient client = new HttpClient();

        public async Task<HttpStatusCode> Add_To_CartAsync(int tovarcod)
        {
            var JsonFormat = new HttpStringContent(JsonConvert.SerializeObject(tovarcod), Windows.Storage.Streams.UnicodeEncoding.Utf8, "application/json");
            HttpResponseMessage response = await client.PostAsync(new Uri("" + _DefaultAPI_URL + "/Cart?user_id=" + (App.Current as App).Current_UserID + ""), JsonFormat);
            return response.StatusCode; //(HttpStatusCode.Ok);
        }

    }
}
