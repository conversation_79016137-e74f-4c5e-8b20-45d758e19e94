﻿<Page
    x:Class="MyStoreCOM.Pages.ApplicationShell"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:MyStoreCOM.Pages"
    xmlns:empVM="using:MyStoreCOM.ViewModels.Employee"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <Grid>
        <NavigationView x:Name="NavigationPanel"
                        OpenPaneLength="240"
                        AlwaysShowHeader="True"
                        IsBackButtonVisible="Visible"
                        IsBackEnabled="{x:Bind AppFrame.CanGoBack, Mode=OneWay}"
                        BackRequested="NavigationPanel_BackRequested"
                        Loaded="NavigationPanel_Loaded"
                        SelectionChanged="NavigationPanel_SelectionChanged"
                        IsSettingsVisible="True">
            <NavigationView.AutoSuggestBox>
                <AutoSuggestBox PlaceholderText="Поиск" QueryIcon="Find" Width="200" x:Name="SearchBox"/>
            </NavigationView.AutoSuggestBox>
            <NavigationView.MenuItems>
                <NavigationViewItem
                    x:Name="MainPageItem"
                    Content="Главная"
                    Tag="Main"
                    Icon="Home"/>
                <NavigationViewItemSeparator Visibility="{x:Bind EmpPanelVisibility}"/>
                <NavigationViewItem
                    x:Name="OrdersListEmp"
                    Content="Активные заказы"
                    Tag="ActiveOrdersEmp"
                    Icon="Attach"
                    Visibility="{x:Bind EmpPanelVisibility}"/>
                <NavigationViewItemSeparator Visibility="{x:Bind UserPanelVisibility}"/>
                <NavigationViewItemHeader Content="Личный кабинет" Visibility="{x:Bind UserPanelVisibility}"/>
                <NavigationViewItem
                    x:Name="Account"
                    Content="Профиль"
                    Tag="Account"
                    Visibility="{x:Bind UserPanelVisibility}"
                    Icon="Contact"/>
                <NavigationViewItem
                    x:Name="Orders"
                    Content="Заказы"
                    Tag="Orders"
                    Visibility="{x:Bind UserPanelVisibility}"
                    Icon="Shop"/>
                <NavigationViewItem
                    x:Name="Basket"
                    Content="Корзина"
                    Tag="Basket"
                    Visibility="{x:Bind UserPanelVisibility}">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE7BF;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItemSeparator/>
                <NavigationViewItemHeader Content="Каталог"/>
                <NavigationViewItem
                    x:Name="ComputersMenuItem"
                    Content="Компьютеры"
                    Tag="computers">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE977;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem
                    x:Name="Monobloki"
                    Content="Моноблоки"
                    Tag="monoblocks">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE950;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem
                    x:Name="Laptops"
                    Content="Ноутбуки"
                    Tag="laptops">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE7F8;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem
                    x:Name="SmartPhones"
                    Content="Смартфоны"
                    Tag="smartphones">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE8EA;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem
                    x:Name="Tablets"
                    Content="Планшеты"
                    Tag="tablets">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE70A;"/>  
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
                <NavigationViewItem
                    x:Name="Other"
                    Content="Разное"
                    Tag="other">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE772;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
            </NavigationView.MenuItems>
            <NavigationView.PaneFooter>
                <NavigationViewItem
                    x:Name="SignInBtn"
                    Content="Вход"
                    Tag="SignInBtn"
                    Tapped="SignInBtn_Tapped">
                    <NavigationViewItem.Icon>
                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE748;"/>
                    </NavigationViewItem.Icon>
                </NavigationViewItem>
            </NavigationView.PaneFooter>
            <ScrollViewer x:Name="MainScrollViewer" HorizontalAlignment="Stretch" VerticalScrollBarVisibility="{x:Bind VerticalScrollVisibility, Mode=OneWay}">
                <Frame x:Name="ContentFrame" Navigated="ContentFrame_Navigated" IsTabStop="True">
                    <Frame.ContentTransitions>
                        <TransitionCollection>
                            <NavigationThemeTransition/>
                        </TransitionCollection>
                    </Frame.ContentTransitions>
                </Frame>
            </ScrollViewer>
        </NavigationView>
       <!-- <VisualStateManager.VisualStateGroups>
            <VisualStateGroup>
                <VisualState>
                    <VisualState.StateTriggers>
                        <AdaptiveTrigger
                            MinWindowWidth="{x:Bind NavigationPanel.CompactModeThresholdWidth}"/>
                    </VisualState.StateTriggers>
                    <VisualState.Setters>
                        <Setter Target="ContentFrame.Padding" Value="12,0,20,24"/>
                    </VisualState.Setters>
                </VisualState>
            </VisualStateGroup>
        </VisualStateManager.VisualStateGroups>-->
    </Grid>
</Page>
