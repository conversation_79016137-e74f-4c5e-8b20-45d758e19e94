﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.ObjectModel;
using MyStoreCOM.Models;
using System.Data.SqlClient;

namespace MyStoreCOM.ViewModels
{
    public class PCcomponentsViewModel
    {
        public ObservableCollection<PCcomponent> PcComponenets { get; private set; } = new ObservableCollection<PCcomponent>();

        public string GetAll { get; } = "SELECT [kod_tovara],[naimenovenie],[price],[nalichie],[warranty],[proizvoditel]FROM[MyStoreCOM].[dbo].[Hardware]";

        public void LoadItems(string query)
        {
            using (SqlConnection conn = new SqlConnection((App.Current as App).ConnectionString))
            {
                conn.Open();
                if (conn.State == System.Data.ConnectionState.Open)
                {
                    using (SqlCommand cmd = conn.CreateCommand())
                    {
                        cmd.CommandText = query;
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            int i = 1;
                            while (reader.Read())
                            {
                                var PcComponent = new PCcomponent
                                {
                                    Count = i,
                                    TovarCode = reader.GetInt32(0),
                                    Naimenovanie = reader.GetString(1),
                                    Price = reader.GetInt32(2),
                                    InStock = reader.GetInt32(3),
                                    Warranty = reader.GetInt32(4),
                                    Manufacturer = reader.GetString(5)
                                };
                                PcComponenets.Add(PcComponent);
                                i++;
                            }
                        }
                    }
                }
                conn.Close();
            }
        }
    }
}
