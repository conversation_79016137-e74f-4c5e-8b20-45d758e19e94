﻿using MyStoreCOM_API_database.Models;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using MyStoreCOM_API_database.Services;
using System.Threading.Tasks;

namespace MyStoreCOM_API_database.Controllers
{
    public class CartController : ApiController
    {
        // GET: api/Cart
        public readonly string ConnectionString = @"Data Source=win-hserver;Initial Catalog=MyStoreCOM;Integrated Security=False;User ID=Administrator;Password=****$gOe92;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
        private CartRepository cartRepository = new CartRepository();

        public async Task<IEnumerable<Cartitem>> Get(int user_id)
        {
            return await cartRepository.GetCartitems(user_id);
        }

        public async Task<HttpResponseMessage> Post(int user_id, [FromBody]int tovarkod)
        {
            return await cartRepository.AddToCart(user_id, tovarkod, Request);
        }

        // DELETE: api/Cart/5
        public async Task<HttpResponseMessage> Delete(int user_id, int tovarkod) //removing the item from the user's cart
        {
            return await cartRepository.DeleteItemFromCart(user_id, tovarkod, Request);
        }
    }
}
