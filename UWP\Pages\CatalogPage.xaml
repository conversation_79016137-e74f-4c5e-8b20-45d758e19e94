﻿<Page
    x:Class="MyStoreCOM.Pages.CatalogPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:data="using:MyStoreCOM.Models"
    xmlns:controls="using:Microsoft.Toolkit.Uwp.UI.Controls"
    xmlns:animations="using:Microsoft.Toolkit.Uwp.UI.Animations"
    mc:Ignorable="d"
    Background="{ThemeResource ApplicationPageBackgroundThemeBrush}">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <SolidColorBrush x:Key="ButtonBackground" Color="{ThemeResource SystemAccentColor}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPointerOver" Color="{ThemeResource SystemAccentColorLight1}"/>
                    <SolidColorBrush x:Key="ButtonBackgroundPressed" Color="{ThemeResource SystemAccentColorDark1}"/>
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>
    </Page.Resources>
    <Grid Margin="20">
        <Button x:Name="RetryBTN" Content="Попробовать ещё раз" Visibility="Collapsed" Click="RetryBTN_Click"  HorizontalAlignment="Center" VerticalAlignment="Center"/>
        <ProgressRing Height="100" Width="100" IsEnabled="True" Visibility="{x:Bind Computer.IsLoading, Mode=OneWay}" IsActive="True"/>
        <StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock x:Name="Header" Margin="0,0,0,15" Text="Каталог >" Style="{StaticResource SubheaderTextBlockStyle}"/>
                <TextBlock x:Name="Header2" Margin="3,0,0,15" Text="{x:Bind Computer.HeaderOfPage}" Style="{StaticResource SubheaderTextBlockStyle}"/>
            </StackPanel>
            <GridView x:Name="Grid_VIEW" ItemsSource="{x:Bind Computer.Computers}" HorizontalAlignment="Center" ItemClick="Grid_VIEW_ItemClick" IsItemClickEnabled="True">
                <GridView.ItemTemplate>
                    <DataTemplate x:Name="TovarTemplate" x:DataType="data:Computer">
                        <StackPanel Height="310" Width="280" Margin="12">
                            <controls:ImageEx x:Name="ImageExControl1"
                                          Height="210" 
                                          Width="280" 
                                          Margin="0,12,0,12"
                                          Source="{x:Bind Image_URL}"/>
                            <RelativePanel>
                                <TextBlock x:Name="Title" Text="{x:Bind Naimenovenie}" TextWrapping="Wrap" Style="{StaticResource BodyTextBlockStyle}" animations:Connected.Key="MainImage"/>
                                <TextBlock x:Name="Price" Text="{x:Bind PriceSTR}" Margin="0,12,0,0" RelativePanel.Below="Title" RelativePanel.AlignBottomWithPanel="True" Style="{StaticResource BaseTextBlockStyle}" FontSize="18"/>
                                <Button x:Name="Add_to_cart"
                                    Background="Transparent"
                                    Tag="{x:Bind Kod_tovara}"
                                    RelativePanel.AlignRightWithPanel="True" 
                                    RelativePanel.AlignBottomWith="Price" 
                                    ToolTipService.ToolTip="Добавить товар в корзину" 
                                    ToolTipService.Placement="Bottom"
                                    Click="Add_to_cart_Click">
                                    <StackPanel>
                                        <FontIcon FontFamily="{StaticResource SymbolThemeFontFamily}" Glyph="&#xE7BF;"/>
                                    </StackPanel>
                                </Button>
                            </RelativePanel>
                        </StackPanel>
                    </DataTemplate>
                </GridView.ItemTemplate>
            </GridView>
        </StackPanel>
    </Grid>
</Page>
