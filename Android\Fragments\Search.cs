﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using MyStoreCom.ViewModels;
using FragmentManager = Android.Support.V4.App.FragmentManager;
using FragmentTransaction = Android.Support.V4.App.FragmentTransaction;

namespace MyStoreCom.Fragments
{
    public class Search : Android.Support.V4.App.Fragment
    {
        View myFragmentView;

        string SearchText = null;

        private static readonly SearchVM _searchVM = new SearchVM();

        public Search(string searchtext)
        {
            SearchText = searchtext;
        }

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            myFragmentView = inflater.Inflate(Resource.Layout.search, container, false);

            EditText SearchBox = myFragmentView.FindViewById<EditText>(Resource.Id.searchbox);
            SearchBox.Text = SearchText;

            var progressbar = myFragmentView.FindViewById<ProgressBar>(Resource.Id.search_progressbar);

            Button StartSearchBTN = myFragmentView.FindViewById<Button>(Resource.Id.search_findbtn);
            StartSearchBTN.Click += async delegate
            {
                progressbar.Visibility = ViewStates.Visible;
                await _searchVM.GetSearchResults(SearchBox.Text);
                progressbar.Visibility = ViewStates.Gone;
            };

            ImageButton OpenCodeScannerBTN = myFragmentView.FindViewById<ImageButton>(Resource.Id.search_openScanerBTN);
            OpenCodeScannerBTN.Click += OpenCodeScannerBTN_Click;

            TextView NotFound = myFragmentView.FindViewById<TextView>(Resource.Id.search_notfound);
            if (SearchText != null)
            {
                NotFound.Visibility = ViewStates.Visible;
            }

            return myFragmentView;
        }

        private void OpenCodeScannerBTN_Click(object sender, EventArgs e)
        {

            FragmentManager fragmentManager = Activity.SupportFragmentManager;
            FragmentTransaction trans = fragmentManager.BeginTransaction();
            trans.Replace(Resource.Id.mainFrame, new CodeScanner(), "Code Scanner");
            //Activity.Title = "Результаты поиска";
            trans.Commit();
        }
    }
}